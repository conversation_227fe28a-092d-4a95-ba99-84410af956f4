import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import CustomFoodManager from '@/components/CustomFoodManager';
import { ThemeToggle } from '@/components/theme-toggle';

const CustomFoods = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              className="mr-4 p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-100 mb-2">Custom Foods</h1>
              <p className="text-gray-600 dark:text-gray-300">Manage your custom food database</p>
            </div>
          </div>
          <ThemeToggle />
        </div>

        {/* Custom Food Manager */}
        <CustomFoodManager />
      </div>
    </div>
  );
};

export default CustomFoods;
