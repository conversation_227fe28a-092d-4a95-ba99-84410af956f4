import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import CustomFoodManager from '@/components/CustomFoodManager';

const CustomFoods = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="mr-4 p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-4xl font-bold text-gray-800 mb-2">Custom Foods</h1>
            <p className="text-gray-600">Manage your custom food database</p>
          </div>
        </div>

        {/* Custom Food Manager */}
        <CustomFoodManager />
      </div>
    </div>
  );
};

export default CustomFoods;
