import { useState, useEffect } from 'react';
import { ArrowLeft, Download, Upload, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useNavigate } from 'react-router-dom';
import { ThemeToggle } from '@/components/theme-toggle';
import CalorieGoalSettings from '@/components/CalorieGoalSettings';
import { 
  getUserSettings, 
  exportSettings, 
  importSettings, 
  resetSettingsToDefault,
  type UserSettings 
} from '@/utils/settingsManager';

const Settings = () => {
  const navigate = useNavigate();
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [importMessage, setImportMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    const userSettings = getUserSettings();
    setSettings(userSettings);
  };

  const handleGoalUpdate = (newGoal: number) => {
    if (settings) {
      setSettings({
        ...settings,
        dailyCalorieGoal: newGoal
      });
    }
  };

  const handleExportSettings = () => {
    const data = exportSettings();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `calorie-tracker-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      const result = importSettings(content);
      
      setImportMessage({
        type: result.success ? 'success' : 'error',
        text: result.message
      });

      if (result.success && result.settings) {
        setSettings(result.settings);
      }

      // Clear message after 5 seconds
      setTimeout(() => setImportMessage(null), 5000);
    };
    reader.readAsText(file);
    
    // Reset input
    event.target.value = '';
  };

  const handleResetAllSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
      const defaultSettings = resetSettingsToDefault();
      setSettings(defaultSettings);
      setImportMessage({
        type: 'success',
        text: 'All settings have been reset to default values.'
      });
      
      // Clear message after 5 seconds
      setTimeout(() => setImportMessage(null), 5000);
    }
  };

  if (!settings) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 dark:border-green-400 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              className="mr-4 p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-100 mb-2">Settings</h1>
              <p className="text-gray-600 dark:text-gray-300">Manage your calorie tracker preferences</p>
            </div>
          </div>
          <ThemeToggle />
        </div>

        <div className="space-y-8">
          {/* Import/Export Messages */}
          {importMessage && (
            <Alert className={`${importMessage.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <AlertDescription className={importMessage.type === 'success' ? 'text-green-800' : 'text-red-800'}>
                {importMessage.text}
              </AlertDescription>
            </Alert>
          )}

          {/* Calorie Goal Settings */}
          <CalorieGoalSettings
            currentGoal={settings.dailyCalorieGoal}
            onGoalUpdated={handleGoalUpdate}
          />

          {/* Theme Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-gray-900 dark:text-gray-100">Appearance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Theme</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Choose your preferred color scheme
                  </p>
                </div>
                <ThemeToggle />
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Theme Options</h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• <strong>Light:</strong> Classic bright theme for daytime use</li>
                  <li>• <strong>Dark:</strong> Easy on the eyes for low-light environments</li>
                  <li>• <strong>System:</strong> Automatically matches your device's theme</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Data Management */}
          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={handleExportSettings}
                  variant="outline"
                  className="w-full"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export Settings
                </Button>
                
                <div className="relative">
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportSettings}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    id="import-settings"
                  />
                  <Button variant="outline" className="w-full" asChild>
                    <label htmlFor="import-settings" className="cursor-pointer">
                      <Upload className="mr-2 h-4 w-4" />
                      Import Settings
                    </label>
                  </Button>
                </div>
                
                <Button
                  onClick={handleResetAllSettings}
                  variant="outline"
                  className="w-full text-red-600 border-red-300 hover:bg-red-50"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset All
                </Button>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Data Management Tips</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• <strong>Export:</strong> Download your settings as a backup file</li>
                  <li>• <strong>Import:</strong> Restore settings from a previously exported file</li>
                  <li>• <strong>Reset:</strong> Return all settings to their default values</li>
                  <li>• Settings are automatically saved to your browser's local storage</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Current Settings Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Current Settings Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-1">Daily Calorie Goal</h4>
                  <p className="text-2xl font-bold text-blue-600">{settings.dailyCalorieGoal} calories</p>
                  <p className="text-sm text-blue-700">Your target daily intake</p>
                </div>
                
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-1">Last Updated</h4>
                  <p className="text-lg font-medium text-gray-700">
                    {new Date(settings.lastUpdated).toLocaleDateString()}
                  </p>
                  <p className="text-sm text-gray-600">
                    {new Date(settings.lastUpdated).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* About */}
          <Card>
            <CardHeader>
              <CardTitle>About Calorie Tracker</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm text-gray-600">
              <div>
                <strong>Version:</strong> 1.0.0
              </div>
              <div>
                <strong>Features:</strong> Daily calorie tracking, custom foods, goal setting, data export/import
              </div>
              <div>
                <strong>Food Database:</strong> 499+ Indian foods plus unlimited custom foods
              </div>
              <div>
                <strong>Storage:</strong> All data stored locally in your browser
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings;
