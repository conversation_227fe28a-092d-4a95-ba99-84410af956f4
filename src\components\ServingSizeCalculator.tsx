import { useState, useEffect } from 'react';
import { Calculator, Info, Scale } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  calculateCalories, 
  validateAmount, 
  getSuggestedUnits, 
  formatConversionResult,
  getCommonConversions,
  type UnitOption,
  type ConversionResult 
} from '@/utils/unitConverter';
import { type FoodItem } from '@/utils/customFoodManager';

interface ServingSizeCalculatorProps {
  selectedFood: FoodItem;
  onCalculationUpdate: (result: ConversionResult) => void;
}

const ServingSizeCalculator = ({ selectedFood, onCalculationUpdate }: ServingSizeCalculatorProps) => {
  const [amount, setAmount] = useState('1');
  const [selectedUnit, setSelectedUnit] = useState('100g');
  const [calculationResult, setCalculationResult] = useState<ConversionResult | null>(null);
  const [error, setError] = useState<string>('');
  const [showUnitInfo, setShowUnitInfo] = useState(false);

  // Set default amount based on food type
  useEffect(() => {
    const foodName = selectedFood.name.toLowerCase();
    const isBeverage = foodName.includes('tea') || foodName.includes('coffee') ||
                      foodName.includes('milk') || foodName.includes('drink') ||
                      foodName.includes('lassi') || foodName.includes('juice');

    if (isBeverage) {
      setAmount('250'); // Default to 250ml for beverages (typical cup size)
    } else {
      setAmount('100'); // Default to 100g for solid foods
    }
  }, [selectedFood.id, selectedFood.name]);

  // Get suggested units based on the food's serving description and name
  const suggestedUnits = getSuggestedUnits(selectedFood.serving, selectedFood.name);

  // Default to the first suggested unit or appropriate base unit
  useEffect(() => {
    if (suggestedUnits.length > 0) {
      // For beverages, prefer ml over 100ml if available
      const foodName = selectedFood.name.toLowerCase();
      const isBeverage = foodName.includes('tea') || foodName.includes('coffee') ||
                        foodName.includes('milk') || foodName.includes('drink') ||
                        foodName.includes('lassi') || foodName.includes('juice');

      if (isBeverage && suggestedUnits.find(u => u.value === 'ml')) {
        setSelectedUnit('ml');
      } else {
        setSelectedUnit(suggestedUnits[0].value);
      }
    } else {
      setSelectedUnit('100g'); // Fallback to 100g
    }
  }, [selectedFood.id, selectedFood.name, suggestedUnits]);

  // Calculate calories whenever amount or unit changes
  useEffect(() => {
    calculateResult();
  }, [amount, selectedUnit, selectedFood]);

  const calculateResult = () => {
    setError('');

    const amountNum = parseFloat(amount);
    const validation = validateAmount(amountNum);

    if (!validation.isValid) {
      setError(validation.error || 'Invalid amount');
      setCalculationResult(null);
      return;
    }

    try {
      const result = calculateCalories(amountNum, selectedUnit, selectedFood.calories, selectedFood.protein || 0);
      setCalculationResult(result);
      onCalculationUpdate(result);
    } catch (err) {
      setError(`Calculation error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setCalculationResult(null);
    }
  };

  const handleAmountChange = (value: string) => {
    setAmount(value);
  };

  const handleUnitChange = (value: string) => {
    setSelectedUnit(value);
  };

  const getUnitTypeLabel = (units: UnitOption[]): string => {
    if (units.length === 0) return '';
    const type = units[0].type;
    switch (type) {
      case 'weight': return 'Weight';
      case 'volume': return 'Volume';
      case 'piece': return 'Count';
      default: return '';
    }
  };

  return (
    <Card className="w-full bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center text-lg text-gray-900 dark:text-gray-100">
          <Calculator className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
          Serving Size Calculator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 max-h-96 overflow-y-auto scroll-smooth">
        {/* Selected Food Info */}
        <div className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700">
          <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">{selectedFood.name}</h3>
          <p className="text-sm text-blue-700 dark:text-blue-300">
            {selectedFood.calories} calories per {selectedFood.serving}
          </p>
        </div>

        {/* Amount and Unit Selection */}
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <Label htmlFor="amount" className="text-gray-900 dark:text-gray-100">Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="1"
              value={amount}
              onChange={(e) => handleAmountChange(e.target.value)}
              min="0.1"
              step="0.1"
              className="w-full bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="unit" className="text-gray-900 dark:text-gray-100">Unit</Label>
            <Select value={selectedUnit} onValueChange={handleUnitChange}>
              <SelectTrigger className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100">
                <SelectValue placeholder="Select unit" />
              </SelectTrigger>
              <SelectContent
                className="max-h-60 overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                position="popper"
                sideOffset={4}
              >
                {suggestedUnits.map((unit) => (
                  <SelectItem key={unit.value} value={unit.value} className="text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700">
                    {unit.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert className="border-red-200 bg-red-50 dark:border-red-700 dark:bg-red-900/20">
            <AlertDescription className="text-red-800 dark:text-red-200">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Calculation Result */}
        {calculationResult && !error && (
          <div className="p-4 bg-green-50 dark:bg-green-900/30 rounded-lg border border-green-200 dark:border-green-700">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-green-900 dark:text-green-100">Calculation Result</span>
              <div className="flex gap-2">
                <Badge variant="secondary" className="bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100">
                  <Scale className="mr-1 h-3 w-3" />
                  {calculationResult.calories} cal
                </Badge>
                <Badge variant="secondary" className="bg-orange-100 dark:bg-orange-800 text-orange-800 dark:text-orange-100">
                  {calculationResult.protein}g protein
                </Badge>
              </div>
            </div>
            <p className="text-sm text-green-700 dark:text-green-300">
              {formatConversionResult(calculationResult)}
            </p>
            {calculationResult.baseEquivalent !== calculationResult.amount && (
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                Equivalent to {calculationResult.baseEquivalent.toFixed(1)} base units
              </p>
            )}
          </div>
        )}

        {/* Unit Information */}
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowUnitInfo(!showUnitInfo)}
            className="w-full"
          >
            <Info className="mr-2 h-4 w-4" />
            {showUnitInfo ? 'Hide' : 'Show'} Unit Information
          </Button>

          {showUnitInfo && (
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                {getUnitTypeLabel(suggestedUnits)} Units Available
              </h4>
              <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                {getCommonConversions(suggestedUnits[0]?.type || 'weight').map((conversion, index) => (
                  <div key={index}>• {conversion}</div>
                ))}
              </div>

              {suggestedUnits.length > 0 && (
                <div className="mt-3">
                  <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-1">Available Units:</h5>
                  <div className="flex flex-wrap gap-1">
                    {suggestedUnits.map((unit) => (
                      <Badge key={unit.value} variant="outline" className="text-xs bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300">
                        {unit.label}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Quick Amount Buttons */}
        <div className="space-y-2">
          <Label className="text-gray-900 dark:text-gray-100">Quick Amounts</Label>
          <div className="grid grid-cols-4 gap-2">
            {(() => {
              const foodName = selectedFood.name.toLowerCase();
              const isBeverage = foodName.includes('tea') || foodName.includes('coffee') ||
                                foodName.includes('milk') || foodName.includes('drink') ||
                                foodName.includes('lassi') || foodName.includes('juice');

              const amounts = isBeverage ? ['100', '250', '500', '1000'] : ['50', '100', '200', '500'];

              return amounts.map((quickAmount) => (
                <Button
                  key={quickAmount}
                  variant="outline"
                  size="sm"
                  onClick={() => setAmount(quickAmount)}
                  className="text-xs"
                >
                  {quickAmount}
                </Button>
              ));
            })()}
          </div>
        </div>

        {/* Tips */}
        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
          <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-1">💡 Tips</h4>
          <ul className="text-xs text-yellow-800 dark:text-yellow-200 space-y-1">
            <li>• Use grams (g) or milliliters (ml) for precise measurements</li>
            <li>• Kitchen scales help with accurate weight measurements</li>
            <li>• 1 cup ≈ 237ml, 1 tablespoon ≈ 15ml, 1 teaspoon ≈ 5ml</li>
            <li>• For packaged foods, check the nutrition label for serving sizes</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ServingSizeCalculator;
