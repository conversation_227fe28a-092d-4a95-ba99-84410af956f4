// Script to convert CSV data to food database format
// Run with: node scripts/csv-to-food-database.js

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the CSV file
const csvPath = path.join(__dirname, '..', 'Indian_Food_Nutrition_Processed.csv');
const csvContent = fs.readFileSync(csvPath, 'utf-8');

// Parse CSV
const lines = csvContent.split('\n');
const headers = lines[0].split(',');
const data = lines.slice(1).filter(line => line.trim() !== '');

// Convert to food database format
const foodItems = data.map((line, index) => {
  const values = line.split(',');
  const dishName = values[0];
  const calories = parseFloat(values[1]);
  
  // Skip if invalid data
  if (!dishName || isNaN(calories)) {
    return null;
  }
  
  return {
    id: index + 1,
    name: dishName,
    calories: Math.round(calories),
    serving: "100g" // Default serving size for Indian foods
  };
}).filter(item => item !== null);

// Generate TypeScript code
const generateFoodDatabase = (items) => {
  let output = `// Indian Food Nutrition Database\n`;
  output += `// Data source: Indian_Food_Nutrition_Processed.csv\n`;
  output += `export const foodDatabase = [\n`;
  
  items.forEach(item => {
    output += `  { id: ${item.id}, name: "${item.name}", calories: ${item.calories}, serving: "${item.serving}" },\n`;
  });
  
  output += `];\n`;
  return output;
};

// Write to file
const outputPath = path.join(__dirname, '..', 'src', 'utils', 'foodDatabase-generated.ts');
const generatedCode = generateFoodDatabase(foodItems);
fs.writeFileSync(outputPath, generatedCode);

console.log(`Generated food database with ${foodItems.length} items`);
console.log(`Output written to: ${outputPath}`);

// Also output some sample entries for manual review
console.log('\nSample entries:');
foodItems.slice(0, 10).forEach(item => {
  console.log(`  ${item.name}: ${item.calories} calories per ${item.serving}`);
});
