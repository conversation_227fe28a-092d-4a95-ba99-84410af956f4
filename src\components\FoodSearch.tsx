
import { useState, useEffect } from 'react';
import { Search, X, Plus, Trash2, Calculator } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getAllFoods } from '@/utils/foodDatabase';
import { removeCustomFood, type FoodItem } from '@/utils/customFoodManager';
import { type ConversionResult } from '@/utils/unitConverter';
import CustomFoodForm from './CustomFoodForm';
import ServingSizeCalculator from './ServingSizeCalculator';

interface FoodSearchProps {
  onAddFood: (food: { name: string; calories: number; serving: string }) => void;
  onClose: () => void;
}

const FoodSearch = ({ onAddFood, onClose }: FoodSearchProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFood, setSelectedFood] = useState<FoodItem | null>(null);
  const [servingSize, setServingSize] = useState('1');
  const [showCustomForm, setShowCustomForm] = useState(false);
  const [allFoods, setAllFoods] = useState<FoodItem[]>([]);
  const [calculationResult, setCalculationResult] = useState<ConversionResult | null>(null);
  const [activeTab, setActiveTab] = useState('simple');

  // Load all foods (base + custom) on component mount
  useEffect(() => {
    setAllFoods(getAllFoods());
  }, []);

  // Refresh foods list when custom food is added
  const refreshFoodsList = () => {
    setAllFoods(getAllFoods());
  };

  const filteredFoods = allFoods.filter(food =>
    food.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddFood = () => {
    if (selectedFood) {
      if (activeTab === 'advanced' && calculationResult) {
        // Use advanced calculator result
        onAddFood({
          name: `${selectedFood.name} (${calculationResult.amount} ${calculationResult.unit})`,
          calories: calculationResult.calories,
          serving: `${calculationResult.amount} ${calculationResult.unit}`
        });
      } else if (activeTab === 'simple' && servingSize) {
        // Use simple multiplier
        const multiplier = parseFloat(servingSize) || 1;
        onAddFood({
          name: `${selectedFood.name} (${servingSize} ${selectedFood.serving})`,
          calories: Math.round(selectedFood.calories * multiplier),
          serving: `${servingSize} ${selectedFood.serving}`
        });
      }
    }
  };

  const handleCalculationUpdate = (result: ConversionResult) => {
    setCalculationResult(result);
  };

  const handleCustomFoodAdded = (newFood: FoodItem) => {
    refreshFoodsList();
    setShowCustomForm(false);
    // Auto-select the newly added custom food
    setSelectedFood(newFood);
    setSearchTerm(''); // Clear search to show the new food
  };

  const handleDeleteCustomFood = (foodId: number, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent selecting the food when clicking delete
    if (confirm('Are you sure you want to delete this custom food?')) {
      removeCustomFood(foodId);
      refreshFoodsList();
      // Clear selection if the deleted food was selected
      if (selectedFood?.id === foodId) {
        setSelectedFood(null);
      }
    }
  };

  if (showCustomForm) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
        <CustomFoodForm
          onFoodAdded={handleCustomFoodAdded}
          onCancel={() => setShowCustomForm(false)}
          existingFoods={allFoods}
        />
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[80vh] bg-white flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between flex-shrink-0">
          <CardTitle>Add Food</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="space-y-4 overflow-y-auto flex-1 min-h-0 scroll-smooth">
          {/* Search Input and Custom Food Button */}
          <div className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search for food..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex justify-center">
              <Button
                onClick={() => setShowCustomForm(true)}
                variant="outline"
                className="text-green-600 border-green-600 hover:bg-green-50"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Custom Food
              </Button>
            </div>
          </div>

          {/* Food List */}
          <div className="max-h-60 overflow-y-auto space-y-2">
            {filteredFoods.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No foods found matching "{searchTerm}"</p>
                <p className="text-sm mt-2">Try a different search term or add a custom food.</p>
              </div>
            ) : (
              filteredFoods.map((food) => (
                <div
                  key={food.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors relative ${
                    selectedFood?.id === food.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedFood(food)}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1 pr-2">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">{food.name}</h3>
                        {food.isCustom && (
                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                            Custom
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        {food.calories} cal per {food.serving}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-right">
                        <div className="font-bold text-blue-600">{food.calories}</div>
                        <div className="text-xs text-gray-500">calories</div>
                      </div>
                      {food.isCustom && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-8 w-8"
                          onClick={(e) => handleDeleteCustomFood(food.id, e)}
                          title="Delete custom food"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Serving Size Calculation */}
          {selectedFood && (
            <div className="space-y-4">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="simple">Simple</TabsTrigger>
                  <TabsTrigger value="advanced">
                    <Calculator className="mr-2 h-4 w-4" />
                    Advanced
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="simple" className="space-y-3">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium mb-2">Selected: {selectedFood.name}</h3>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        placeholder="1"
                        value={servingSize}
                        onChange={(e) => setServingSize(e.target.value)}
                        className="w-20"
                        min="0.1"
                        step="0.1"
                      />
                      <span className="text-sm text-gray-600">{selectedFood.serving}</span>
                      <span className="text-sm text-gray-400">=</span>
                      <span className="font-bold text-blue-600">
                        {Math.round(selectedFood.calories * (parseFloat(servingSize) || 1))} cal
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Multiply the standard serving size by your desired amount
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-3">
                  <ServingSizeCalculator
                    selectedFood={selectedFood}
                    onCalculationUpdate={handleCalculationUpdate}
                  />
                </TabsContent>
              </Tabs>
            </div>
          )}

          {selectedFood && activeTab === 'advanced' && !calculationResult && (
            <p className="text-xs text-gray-500 text-center mt-2">
              Enter amount and unit to calculate calories
            </p>
          )}
        </CardContent>

        {/* Action Buttons - Fixed at bottom */}
        <div className="flex justify-end space-x-2 p-6 pt-4 border-t bg-white flex-shrink-0">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleAddFood}
            disabled={!selectedFood || (activeTab === 'advanced' && !calculationResult)}
            className="bg-green-600 hover:bg-green-700"
          >
            Add Food
            {selectedFood && activeTab === 'simple' && servingSize && (
              <span className="ml-2 text-green-100">
                ({Math.round(selectedFood.calories * (parseFloat(servingSize) || 1))} cal)
              </span>
            )}
            {selectedFood && activeTab === 'advanced' && calculationResult && (
              <span className="ml-2 text-green-100">
                ({calculationResult.calories} cal)
              </span>
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default FoodSearch;
