
import { useState, useEffect } from 'react';
import { Search, X, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getAllFoods } from '@/utils/foodDatabase';
import { removeCustomFood, type FoodItem } from '@/utils/customFoodManager';
import { type ConversionResult } from '@/utils/unitConverter';
import CustomFoodForm from './CustomFoodForm';
import ServingSizeCalculator from './ServingSizeCalculator';

interface FoodSearchProps {
  onAddFood: (food: { name: string; calories: number; protein: number; serving: string }) => void;
  onClose: () => void;
}

const FoodSearch = ({ onAddFood, onClose }: FoodSearchProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFood, setSelectedFood] = useState<FoodItem | null>(null);
  const [showCustomForm, setShowCustomForm] = useState(false);
  const [allFoods, setAllFoods] = useState<FoodItem[]>([]);
  const [calculationResult, setCalculationResult] = useState<ConversionResult | null>(null);

  // Load all foods (base + custom) on component mount
  useEffect(() => {
    setAllFoods(getAllFoods());
  }, []);

  // Refresh foods list when custom food is added
  const refreshFoodsList = () => {
    setAllFoods(getAllFoods());
  };

  const filteredFoods = allFoods.filter(food =>
    food.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddFood = () => {
    if (selectedFood && calculationResult) {
      // Calculate protein proportionally based on the serving size
      const proteinRatio = calculationResult.calories / selectedFood.calories;
      const calculatedProtein = (selectedFood.protein || 0) * proteinRatio;

      // Use advanced calculator result
      onAddFood({
        name: `${selectedFood.name} (${calculationResult.amount} ${calculationResult.unit})`,
        calories: calculationResult.calories,
        protein: calculatedProtein,
        serving: `${calculationResult.amount} ${calculationResult.unit}`
      });
    }
  };

  const handleCalculationUpdate = (result: ConversionResult) => {
    setCalculationResult(result);
  };

  const handleCustomFoodAdded = (newFood: FoodItem) => {
    refreshFoodsList();
    setShowCustomForm(false);
    // Auto-select the newly added custom food
    setSelectedFood(newFood);
    setSearchTerm(''); // Clear search to show the new food
  };

  const handleDeleteCustomFood = (foodId: number, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent selecting the food when clicking delete
    if (confirm('Are you sure you want to delete this custom food?')) {
      removeCustomFood(foodId);
      refreshFoodsList();
      // Clear selection if the deleted food was selected
      if (selectedFood?.id === foodId) {
        setSelectedFood(null);
      }
    }
  };

  if (showCustomForm) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
        <CustomFoodForm
          onFoodAdded={handleCustomFoodAdded}
          onCancel={() => setShowCustomForm(false)}
          existingFoods={allFoods}
        />
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[80vh] bg-white dark:bg-gray-800 flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between flex-shrink-0">
          <CardTitle className="text-gray-900 dark:text-gray-100">Add Food</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="space-y-4 overflow-y-auto flex-1 min-h-0 scroll-smooth">
          {/* Search Input and Custom Food Button */}
          <div className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
              <Input
                placeholder="Search for food..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400"
              />
            </div>
            <div className="flex justify-center">
              <Button
                onClick={() => setShowCustomForm(true)}
                variant="outline"
                className="text-green-600 dark:text-green-400 border-green-600 dark:border-green-500 hover:bg-green-50 dark:hover:bg-green-900/20"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Custom Food
              </Button>
            </div>
          </div>

          {/* Food List */}
          <div className="max-h-60 overflow-y-auto space-y-2">
            {filteredFoods.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <p>No foods found matching "{searchTerm}"</p>
                <p className="text-sm mt-2">Try a different search term or add a custom food.</p>
              </div>
            ) : (
              filteredFoods.map((food) => (
                <div
                  key={food.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors relative bg-white dark:bg-gray-800 ${
                    selectedFood?.id === food.id
                      ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => setSelectedFood(food)}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1 pr-2">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">{food.name}</h3>
                        {food.isCustom && (
                          <Badge variant="secondary" className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                            Custom
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {food.calories} cal, {(food.protein || 0).toFixed(1)}g protein per {food.serving}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-right">
                        <div className="font-bold text-blue-600 dark:text-blue-400">{food.calories}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">calories</div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-orange-600 dark:text-orange-400">{(food.protein || 0).toFixed(1)}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">protein (g)</div>
                      </div>
                      {food.isCustom && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 p-1 h-8 w-8"
                          onClick={(e) => handleDeleteCustomFood(food.id, e)}
                          title="Delete custom food"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Serving Size Calculation */}
          {selectedFood && (
            <div className="space-y-4">
              <ServingSizeCalculator
                selectedFood={selectedFood}
                onCalculationUpdate={handleCalculationUpdate}
              />
            </div>
          )}

          {selectedFood && !calculationResult && (
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
              Enter amount and unit to calculate calories
            </p>
          )}
        </CardContent>

        {/* Action Buttons - Fixed at bottom */}
        <div className="flex justify-end space-x-2 p-6 pt-4 border-t bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 flex-shrink-0">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleAddFood}
            disabled={!selectedFood || !calculationResult}
            className="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600"
          >
            Add Food
            {selectedFood && calculationResult && (
              <span className="ml-2 text-green-100">
                ({calculationResult.calories} cal, {calculationResult.protein}g protein)
              </span>
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default FoodSearch;
