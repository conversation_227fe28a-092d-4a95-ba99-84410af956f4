
import { useState, useEffect } from 'react';
import { Plus, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import FoodSearch from '@/components/FoodSearch';
import DailyLog from '@/components/DailyLog';
import CalorieStats from '@/components/CalorieStats';
import CalorieGoalSettings from '@/components/CalorieGoalSettings';
import DailyStatsCard from '@/components/DailyStatsCard';
import { getUserSettings, type UserSettings } from '@/utils/settingsManager';
import {
  getTodayEntries,
  addFoodEntry as addDailyFoodEntry,
  removeFoodEntry as removeDailyFoodEntry,
  getTodayTotalCalories,
  checkAndResetIfNewDay,
  type DailyFoodEntry
} from '@/utils/dailyLogManager';

// Re-export DailyFoodEntry as FoodEntry for backward compatibility
export type FoodEntry = DailyFoodEntry;

const Index = () => {
  const [dailyEntries, setDailyEntries] = useState<DailyFoodEntry[]>([]);
  const [showFoodSearch, setShowFoodSearch] = useState(false);
  const [showGoalSettings, setShowGoalSettings] = useState(false);
  const [dailyGoal, setDailyGoal] = useState(2000);
  const navigate = useNavigate();

  // Load entries and settings on component mount
  useEffect(() => {
    // Check if it's a new day and reset if needed
    const isNewDay = checkAndResetIfNewDay();
    if (isNewDay) {
      toast({
        title: "New Day Started!",
        description: "Your calorie tracker has been reset for today.",
      });
    }

    // Load today's entries
    const todayEntries = getTodayEntries();
    setDailyEntries(todayEntries);

    // Load user settings
    const settings = getUserSettings();
    setDailyGoal(settings.dailyCalorieGoal);
  }, []);

  // Periodic check for new day (every 5 minutes)
  useEffect(() => {
    const interval = setInterval(() => {
      const isNewDay = checkAndResetIfNewDay();
      if (isNewDay) {
        // Reset the entries for the new day
        setDailyEntries([]);
        toast({
          title: "New Day Started!",
          description: "Your calorie tracker has been reset for today.",
        });
      }
    }, 5 * 60 * 1000); // Check every 5 minutes

    return () => clearInterval(interval);
  }, []);

  const addFoodEntry = (food: Omit<DailyFoodEntry, 'id' | 'timestamp'>) => {
    const newEntry = addDailyFoodEntry(food);
    setDailyEntries(prev => [...prev, newEntry]);
    setShowFoodSearch(false);
    toast({
      title: "Food Added!",
      description: `${food.name} has been added to your daily log.`,
    });
  };

  const removeFoodEntry = (id: string) => {
    const success = removeDailyFoodEntry(id);
    if (success) {
      setDailyEntries(prev => prev.filter(entry => entry.id !== id));
      toast({
        title: "Food Removed",
        description: "Item has been removed from your daily log.",
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to remove item from your daily log.",
        variant: "destructive"
      });
    }
  };

  const handleGoalUpdate = (newGoal: number) => {
    setDailyGoal(newGoal);
    toast({
      title: "Goal Updated!",
      description: `Your daily calorie goal has been set to ${newGoal} calories.`,
    });
  };

  const totalCalories = dailyEntries.reduce((sum, entry) => sum + entry.calories, 0);
  const remainingCalories = dailyGoal - totalCalories;
  const progressPercentage = Math.min((totalCalories / dailyGoal) * 100, 100);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8 relative">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">Calorie Tracker</h1>
          <p className="text-gray-600">Track your daily nutrition and reach your goals</p>

          {/* Navigation Buttons */}
          <div className="absolute top-0 right-0 flex space-x-2">
            <Button
              variant="outline"
              onClick={() => navigate('/custom-foods')}
              className="text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              <Plus className="mr-2 h-4 w-4" />
              Custom Foods
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/settings')}
              className="text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <CalorieStats
          totalCalories={totalCalories}
          dailyGoal={dailyGoal}
          remainingCalories={remainingCalories}
          progressPercentage={progressPercentage}
          onEditGoal={() => setShowGoalSettings(true)}
        />

        {/* Action Buttons */}
        <div className="flex justify-center mb-8">
          <Button 
            onClick={() => setShowFoodSearch(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg rounded-xl shadow-lg transition-all duration-200 hover:scale-105"
          >
            <Plus className="mr-2 h-5 w-5" />
            Add Food
          </Button>
        </div>

        {/* Daily Progress */}
        <Card className="mb-8 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-xl text-gray-800">Daily Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Consumed: {totalCalories} cal</span>
                <span>Goal: {dailyGoal} cal</span>
              </div>
              <Progress value={progressPercentage} className="h-3" />
              <div className="text-center">
                {remainingCalories > 0 ? (
                  <p className="text-green-600 font-medium">
                    {remainingCalories} calories remaining
                  </p>
                ) : (
                  <p className="text-red-500 font-medium">
                    {Math.abs(remainingCalories)} calories over goal
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Daily Statistics */}
        <DailyStatsCard
          totalCalories={totalCalories}
          dailyGoal={dailyGoal}
        />

        {/* Daily Log */}
        <DailyLog entries={dailyEntries} onRemoveEntry={removeFoodEntry} />

        {/* Food Search Modal */}
        {showFoodSearch && (
          <FoodSearch
            onAddFood={addFoodEntry}
            onClose={() => setShowFoodSearch(false)}
          />
        )}

        {/* Calorie Goal Settings Modal */}
        {showGoalSettings && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="relative">
              <CalorieGoalSettings
                currentGoal={dailyGoal}
                onGoalUpdated={(newGoal) => {
                  handleGoalUpdate(newGoal);
                  setShowGoalSettings(false);
                }}
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowGoalSettings(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                ✕
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Index;
