import { useState } from 'react';
import { Plus, AlertCircle, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { addCustomFood, validateCustomFood, foodNameExists, type FoodItem } from '@/utils/customFoodManager';

interface CustomFoodFormProps {
  onFoodAdded: (food: FoodItem) => void;
  onCancel: () => void;
  existingFoods: FoodItem[];
}

const CustomFoodForm = ({ onFoodAdded, onCancel, existingFoods }: CustomFoodFormProps) => {
  const [formData, setFormData] = useState({
    name: '',
    calories: '',
    serving: ''
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors([]);

    // Validate form data
    const caloriesNum = parseFloat(formData.calories);
    const validation = validateCustomFood({
      name: formData.name,
      calories: caloriesNum,
      serving: formData.serving
    });

    if (!validation.isValid) {
      setErrors(validation.errors);
      setIsSubmitting(false);
      return;
    }

    // Check if food name already exists
    if (foodNameExists(formData.name, existingFoods)) {
      setErrors(['A food with this name already exists']);
      setIsSubmitting(false);
      return;
    }

    try {
      // Add the custom food
      const newCustomFood = addCustomFood({
        name: formData.name.trim(),
        calories: caloriesNum,
        serving: formData.serving.trim()
      });

      // Show success message
      setShowSuccess(true);
      
      // Call the callback with the new food
      onFoodAdded({
        id: newCustomFood.id,
        name: newCustomFood.name,
        calories: newCustomFood.calories,
        serving: newCustomFood.serving,
        isCustom: true
      });

      // Reset form
      setFormData({ name: '', calories: '', serving: '' });
      
      // Hide success message after 2 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 2000);

    } catch (error) {
      setErrors(['Failed to add custom food. Please try again.']);
    }

    setIsSubmitting(false);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Plus className="mr-2 h-5 w-5 text-green-600" />
          Add Custom Food
        </CardTitle>
      </CardHeader>
      <CardContent>
        {showSuccess && (
          <Alert className="mb-4 border-green-200 bg-green-50">
            <Check className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Custom food added successfully!
            </AlertDescription>
          </Alert>
        )}

        {errors.length > 0 && (
          <Alert className="mb-4 border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <ul className="list-disc list-inside space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="food-name">Food Name *</Label>
            <Input
              id="food-name"
              type="text"
              placeholder="e.g., Homemade Pizza"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full"
              maxLength={100}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="calories">Calories per serving *</Label>
            <Input
              id="calories"
              type="number"
              placeholder="e.g., 250"
              value={formData.calories}
              onChange={(e) => handleInputChange('calories', e.target.value)}
              className="w-full"
              min="1"
              max="10000"
              step="1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="serving">Serving Size *</Label>
            <Input
              id="serving"
              type="text"
              placeholder="e.g., 1 slice, 100g, 1 cup"
              value={formData.serving}
              onChange={(e) => handleInputChange('serving', e.target.value)}
              className="w-full"
              maxLength={50}
            />
          </div>

          <div className="flex space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-green-600 hover:bg-green-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Adding...' : 'Add Food'}
            </Button>
          </div>
        </form>

        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Tip:</strong> Make sure to enter accurate calorie information. 
            You can find nutrition facts on food packaging or nutrition websites.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomFoodForm;
