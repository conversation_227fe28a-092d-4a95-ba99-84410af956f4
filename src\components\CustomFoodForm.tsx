import { useState } from 'react';
import { Plus, AlertCircle, Check, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { addCustomFood, validateCustomFood, foodNameExists, type FoodItem } from '@/utils/customFoodManager';
import { UNIT_OPTIONS } from '@/utils/unitConverter';

interface CustomFoodFormProps {
  onFoodAdded: (food: FoodItem) => void;
  onCancel: () => void;
  existingFoods: FoodItem[];
}

const CustomFoodForm = ({ onFoodAdded, onCancel, existingFoods }: CustomFoodFormProps) => {
  const [formData, setFormData] = useState({
    name: '',
    calories: '',
    protein: '',
    serving: ''
  });
  const [selectedUnit, setSelectedUnit] = useState('100g');
  const [errors, setErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showUnitHelper, setShowUnitHelper] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors([]);

    // Validate form data
    const caloriesNum = parseFloat(formData.calories);
    const proteinNum = parseFloat(formData.protein) || 0; // Default to 0 if empty
    const validation = validateCustomFood({
      name: formData.name,
      calories: caloriesNum,
      protein: proteinNum,
      serving: formData.serving
    });

    if (!validation.isValid) {
      setErrors(validation.errors);
      setIsSubmitting(false);
      return;
    }

    // Check if food name already exists
    if (foodNameExists(formData.name, existingFoods)) {
      setErrors(['A food with this name already exists']);
      setIsSubmitting(false);
      return;
    }

    try {
      // Add the custom food
      const newCustomFood = addCustomFood({
        name: formData.name.trim(),
        calories: caloriesNum,
        protein: proteinNum,
        serving: formData.serving.trim()
      });

      // Show success message
      setShowSuccess(true);
      
      // Call the callback with the new food
      onFoodAdded({
        id: newCustomFood.id,
        name: newCustomFood.name,
        calories: newCustomFood.calories,
        protein: newCustomFood.protein,
        serving: newCustomFood.serving,
        isCustom: true
      });

      // Reset form
      setFormData({ name: '', calories: '', protein: '', serving: '' });
      
      // Hide success message after 2 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 2000);

    } catch (error) {
      setErrors(['Failed to add custom food. Please try again.']);
    }

    setIsSubmitting(false);
  };

  return (
    <Card className="w-full max-w-md mx-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center text-lg text-gray-900 dark:text-gray-100">
          <Plus className="mr-2 h-5 w-5 text-green-600 dark:text-green-400" />
          Add Custom Food
        </CardTitle>
      </CardHeader>
      <CardContent>
        {showSuccess && (
          <Alert className="mb-4 border-green-200 bg-green-50 dark:border-green-700 dark:bg-green-900/20">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertDescription className="text-green-800 dark:text-green-200">
              Custom food added successfully!
            </AlertDescription>
          </Alert>
        )}

        {errors.length > 0 && (
          <Alert className="mb-4 border-red-200 bg-red-50 dark:border-red-700 dark:bg-red-900/20">
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
            <AlertDescription className="text-red-800 dark:text-red-200">
              <ul className="list-disc list-inside space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="food-name" className="text-gray-900 dark:text-gray-100">Food Name *</Label>
            <Input
              id="food-name"
              type="text"
              placeholder="e.g., Homemade Pizza"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
              maxLength={100}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="calories" className="text-gray-900 dark:text-gray-100">Calories per serving *</Label>
            <Input
              id="calories"
              type="number"
              placeholder="e.g., 250"
              value={formData.calories}
              onChange={(e) => handleInputChange('calories', e.target.value)}
              className="w-full bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
              min="1"
              max="10000"
              step="1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="protein" className="text-gray-900 dark:text-gray-100">Protein per serving (grams)</Label>
            <Input
              id="protein"
              type="number"
              placeholder="e.g., 12.5"
              value={formData.protein}
              onChange={(e) => handleInputChange('protein', e.target.value)}
              className="w-full bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
              min="0"
              max="1000"
              step="0.1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="serving" className="text-gray-900 dark:text-gray-100">Serving Size *</Label>
            <div className="flex space-x-2">
              <Input
                id="serving"
                type="text"
                placeholder="e.g., 1 slice, 100g, 1 cup"
                value={formData.serving}
                onChange={(e) => handleInputChange('serving', e.target.value)}
                className="flex-1 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                maxLength={50}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowUnitHelper(!showUnitHelper)}
                className="px-3"
              >
                <Info className="h-4 w-4" />
              </Button>
            </div>

            {showUnitHelper && (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Common Serving Sizes</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <strong className="text-gray-900 dark:text-gray-100">Weight:</strong>
                    <div className="text-blue-700 dark:text-blue-300">100g, 1 kg, 1 oz</div>
                  </div>
                  <div>
                    <strong className="text-gray-900 dark:text-gray-100">Volume:</strong>
                    <div className="text-blue-700 dark:text-blue-300">100ml, 1 cup, 1 tbsp</div>
                  </div>
                  <div>
                    <strong className="text-gray-900 dark:text-gray-100">Count:</strong>
                    <div className="text-blue-700 dark:text-blue-300">1 piece, 1 slice, 1 serving</div>
                  </div>
                  <div>
                    <strong className="text-gray-900 dark:text-gray-100">Examples:</strong>
                    <div className="text-blue-700 dark:text-blue-300">1 medium apple, 2 cookies</div>
                  </div>
                </div>
                <div className="mt-2 text-xs text-blue-600 dark:text-blue-300">
                  💡 Use specific units like "100g" or "1 cup" for better portion tracking
                </div>
              </div>
            )}
          </div>

          <div className="flex space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Adding...' : 'Add Food'}
            </Button>
          </div>
        </form>

        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong className="text-gray-900 dark:text-gray-100">Tip:</strong> Make sure to enter accurate calorie information.
            You can find nutrition facts on food packaging or nutrition websites.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomFoodForm;
