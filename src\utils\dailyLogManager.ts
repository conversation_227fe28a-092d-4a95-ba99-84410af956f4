// Daily Food Log Management Utility
// Handles daily food entries with automatic reset and historical data

export interface DailyFoodEntry {
  id: string;
  name: string;
  calories: number;
  protein: number;
  serving: string;
  timestamp: Date;
}

export interface DailyLogSummary {
  date: string;
  totalCalories: number;
  totalProtein: number;
  entryCount: number;
  entries: DailyFoodEntry[];
}

export interface WeeklyStats {
  averageCalories: number;
  averageProtein: number;
  totalDays: number;
  highestDay: { date: string; calories: number };
  lowestDay: { date: string; calories: number };
}

const DAILY_LOG_PREFIX = 'calorie-entries-';
const DAILY_SUMMARY_PREFIX = 'daily-summary-';

// Get today's date string in YYYY-MM-DD format
export const getTodayDateString = (): string => {
  return new Date().toISOString().split('T')[0];
};

// Get date string for any date
export const getDateString = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// Check if it's a new day and reset if needed
export const checkAndResetIfNewDay = (): boolean => {
  const today = getTodayDateString();
  const lastActiveDate = localStorage.getItem('last-active-date');
  
  if (lastActiveDate !== today) {
    // Save yesterday's summary if there was data
    if (lastActiveDate) {
      saveDailySummary(lastActiveDate);
    }
    
    // Update last active date
    localStorage.setItem('last-active-date', today);
    return true; // New day detected
  }
  
  return false; // Same day
};

// Get today's food entries
export const getTodayEntries = (): DailyFoodEntry[] => {
  try {
    const today = getTodayDateString();
    const savedEntries = localStorage.getItem(`${DAILY_LOG_PREFIX}${today}`);
    
    if (savedEntries) {
      const entries = JSON.parse(savedEntries);
      return entries.map((entry: any) => ({
        ...entry,
        timestamp: new Date(entry.timestamp)
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Error loading today\'s entries:', error);
    return [];
  }
};

// Save today's food entries
export const saveTodayEntries = (entries: DailyFoodEntry[]): void => {
  try {
    const today = getTodayDateString();
    localStorage.setItem(`${DAILY_LOG_PREFIX}${today}`, JSON.stringify(entries));
  } catch (error) {
    console.error('Error saving today\'s entries:', error);
  }
};

// Add a food entry to today's log
export const addFoodEntry = (food: Omit<DailyFoodEntry, 'id' | 'timestamp'>): DailyFoodEntry => {
  const newEntry: DailyFoodEntry = {
    ...food,
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    timestamp: new Date()
  };
  
  const currentEntries = getTodayEntries();
  const updatedEntries = [...currentEntries, newEntry];
  saveTodayEntries(updatedEntries);
  
  return newEntry;
};

// Remove a food entry from today's log
export const removeFoodEntry = (entryId: string): boolean => {
  try {
    const currentEntries = getTodayEntries();
    const updatedEntries = currentEntries.filter(entry => entry.id !== entryId);
    saveTodayEntries(updatedEntries);
    return true;
  } catch (error) {
    console.error('Error removing food entry:', error);
    return false;
  }
};

// Get today's total calories
export const getTodayTotalCalories = (): number => {
  const entries = getTodayEntries();
  return entries.reduce((total, entry) => total + entry.calories, 0);
};

// Get today's total protein
export const getTodayTotalProtein = (): number => {
  const entries = getTodayEntries();
  return entries.reduce((total, entry) => total + (entry.protein || 0), 0);
};

// Save daily summary for a specific date
export const saveDailySummary = (dateString: string): void => {
  try {
    const entries = JSON.parse(localStorage.getItem(`${DAILY_LOG_PREFIX}${dateString}`) || '[]');
    
    if (entries.length > 0) {
      const totalCalories = entries.reduce((total: number, entry: any) => total + entry.calories, 0);
      const totalProtein = entries.reduce((total: number, entry: any) => total + (entry.protein || 0), 0);

      const summary: DailyLogSummary = {
        date: dateString,
        totalCalories,
        totalProtein,
        entryCount: entries.length,
        entries: entries.map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp)
        }))
      };
      
      localStorage.setItem(`${DAILY_SUMMARY_PREFIX}${dateString}`, JSON.stringify(summary));
    }
  } catch (error) {
    console.error('Error saving daily summary:', error);
  }
};

// Get daily summary for a specific date
export const getDailySummary = (dateString: string): DailyLogSummary | null => {
  try {
    const summary = localStorage.getItem(`${DAILY_SUMMARY_PREFIX}${dateString}`);
    if (summary) {
      const parsed = JSON.parse(summary);
      return {
        ...parsed,
        entries: parsed.entries.map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp)
        }))
      };
    }
    return null;
  } catch (error) {
    console.error('Error loading daily summary:', error);
    return null;
  }
};

// Get last N days of summaries
export const getRecentDailySummaries = (days: number = 7): DailyLogSummary[] => {
  const summaries: DailyLogSummary[] = [];
  const today = new Date();
  
  for (let i = 1; i <= days; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateString = getDateString(date);
    
    const summary = getDailySummary(dateString);
    if (summary) {
      summaries.push(summary);
    }
  }
  
  return summaries.reverse(); // Return in chronological order
};

// Get weekly statistics
export const getWeeklyStats = (): WeeklyStats | null => {
  const recentSummaries = getRecentDailySummaries(7);
  
  if (recentSummaries.length === 0) {
    return null;
  }
  
  const totalCalories = recentSummaries.reduce((sum, summary) => sum + summary.totalCalories, 0);
  const totalProtein = recentSummaries.reduce((sum, summary) => sum + (summary.totalProtein || 0), 0);
  const averageCalories = Math.round(totalCalories / recentSummaries.length);
  const averageProtein = Math.round((totalProtein / recentSummaries.length) * 10) / 10; // Round to 1 decimal

  const sortedByCalories = [...recentSummaries].sort((a, b) => a.totalCalories - b.totalCalories);
  const lowestDay = sortedByCalories[0];
  const highestDay = sortedByCalories[sortedByCalories.length - 1];

  return {
    averageCalories,
    averageProtein,
    totalDays: recentSummaries.length,
    highestDay: { date: highestDay.date, calories: highestDay.totalCalories },
    lowestDay: { date: lowestDay.date, calories: lowestDay.totalCalories }
  };
};

// Clear all historical data (for reset/cleanup)
export const clearAllHistoricalData = (): void => {
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith(DAILY_LOG_PREFIX) || key.startsWith(DAILY_SUMMARY_PREFIX)) {
      localStorage.removeItem(key);
    }
  });
  localStorage.removeItem('last-active-date');
};

// Export daily data for backup
export const exportDailyData = (dateString: string): string => {
  const summary = getDailySummary(dateString);
  return JSON.stringify(summary, null, 2);
};

// Export all historical data
export const exportAllHistoricalData = (): string => {
  const data: { [key: string]: any } = {};
  const keys = Object.keys(localStorage);
  
  keys.forEach(key => {
    if (key.startsWith(DAILY_LOG_PREFIX) || key.startsWith(DAILY_SUMMARY_PREFIX)) {
      try {
        data[key] = JSON.parse(localStorage.getItem(key) || '');
      } catch (error) {
        console.error(`Error exporting ${key}:`, error);
      }
    }
  });
  
  return JSON.stringify(data, null, 2);
};

// Get storage usage statistics
export const getStorageStats = (): { totalEntries: number; totalDays: number; oldestDate: string | null } => {
  const keys = Object.keys(localStorage);
  const summaryKeys = keys.filter(key => key.startsWith(DAILY_SUMMARY_PREFIX));
  
  let totalEntries = 0;
  let oldestDate: string | null = null;
  
  summaryKeys.forEach(key => {
    try {
      const summary = JSON.parse(localStorage.getItem(key) || '');
      totalEntries += summary.entryCount || 0;
      
      const date = key.replace(DAILY_SUMMARY_PREFIX, '');
      if (!oldestDate || date < oldestDate) {
        oldestDate = date;
      }
    } catch (error) {
      console.error(`Error reading ${key}:`, error);
    }
  });
  
  return {
    totalEntries,
    totalDays: summaryKeys.length,
    oldestDate
  };
};
