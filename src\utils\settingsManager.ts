// Settings Management Utility
// Handles user preferences and settings

export interface UserSettings {
  dailyCalorieGoal: number;
  lastUpdated: string;
}

const SETTINGS_KEY = 'user-settings';
const DEFAULT_CALORIE_GOAL = 2000;

// Get user settings from localStorage
export const getUserSettings = (): UserSettings => {
  try {
    const stored = localStorage.getItem(SETTINGS_KEY);
    if (stored) {
      const settings = JSON.parse(stored);
      return {
        dailyCalorieGoal: settings.dailyCalorieGoal || DEFAULT_CALORIE_GOAL,
        lastUpdated: settings.lastUpdated || new Date().toISOString()
      };
    }
  } catch (error) {
    console.error('Error loading user settings:', error);
  }
  
  // Return default settings if none exist or error occurred
  return {
    dailyCalorieGoal: DEFAULT_CALORIE_GOAL,
    lastUpdated: new Date().toISOString()
  };
};

// Save user settings to localStorage
export const saveUserSettings = (settings: UserSettings): void => {
  try {
    const settingsToSave = {
      ...settings,
      lastUpdated: new Date().toISOString()
    };
    localStorage.setItem(SETTINGS_KEY, JSON.stringify(settingsToSave));
  } catch (error) {
    console.error('Error saving user settings:', error);
  }
};

// Update daily calorie goal
export const updateDailyCalorieGoal = (newGoal: number): UserSettings => {
  const currentSettings = getUserSettings();
  const updatedSettings: UserSettings = {
    ...currentSettings,
    dailyCalorieGoal: newGoal,
    lastUpdated: new Date().toISOString()
  };
  
  saveUserSettings(updatedSettings);
  return updatedSettings;
};

// Validate calorie goal
export const validateCalorieGoal = (goal: number): { isValid: boolean; error?: string } => {
  if (!goal || isNaN(goal)) {
    return { isValid: false, error: 'Calorie goal must be a valid number' };
  }
  
  if (goal < 800) {
    return { isValid: false, error: 'Calorie goal should be at least 800 calories for health reasons' };
  }
  
  if (goal > 10000) {
    return { isValid: false, error: 'Calorie goal should be less than 10,000 calories' };
  }
  
  if (goal % 1 !== 0) {
    return { isValid: false, error: 'Calorie goal should be a whole number' };
  }
  
  return { isValid: true };
};

// Get recommended calorie goals based on common profiles
export const getRecommendedGoals = () => {
  return [
    { label: 'Weight Loss (Women)', value: 1200, description: 'For sedentary women looking to lose weight' },
    { label: 'Weight Loss (Men)', value: 1500, description: 'For sedentary men looking to lose weight' },
    { label: 'Maintenance (Women)', value: 2000, description: 'For moderately active women' },
    { label: 'Maintenance (Men)', value: 2500, description: 'For moderately active men' },
    { label: 'Weight Gain (Women)', value: 2300, description: 'For women looking to gain weight' },
    { label: 'Weight Gain (Men)', value: 3000, description: 'For men looking to gain weight' },
    { label: 'Athletic (Women)', value: 2800, description: 'For very active women/athletes' },
    { label: 'Athletic (Men)', value: 3500, description: 'For very active men/athletes' }
  ];
};

// Reset settings to default
export const resetSettingsToDefault = (): UserSettings => {
  const defaultSettings: UserSettings = {
    dailyCalorieGoal: DEFAULT_CALORIE_GOAL,
    lastUpdated: new Date().toISOString()
  };
  
  saveUserSettings(defaultSettings);
  return defaultSettings;
};

// Export settings for backup
export const exportSettings = (): string => {
  const settings = getUserSettings();
  return JSON.stringify(settings, null, 2);
};

// Import settings from backup
export const importSettings = (jsonData: string): { success: boolean; message: string; settings?: UserSettings } => {
  try {
    const importedSettings = JSON.parse(jsonData);
    
    if (!importedSettings.dailyCalorieGoal) {
      return { success: false, message: 'Invalid settings format: Missing daily calorie goal' };
    }
    
    const validation = validateCalorieGoal(importedSettings.dailyCalorieGoal);
    if (!validation.isValid) {
      return { success: false, message: `Invalid calorie goal: ${validation.error}` };
    }
    
    const newSettings: UserSettings = {
      dailyCalorieGoal: importedSettings.dailyCalorieGoal,
      lastUpdated: new Date().toISOString()
    };
    
    saveUserSettings(newSettings);
    
    return { 
      success: true, 
      message: 'Settings imported successfully',
      settings: newSettings
    };
  } catch (error) {
    return { success: false, message: 'Invalid JSON format' };
  }
};
