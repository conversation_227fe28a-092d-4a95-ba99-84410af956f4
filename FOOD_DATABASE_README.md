# Indian Food Nutrition Database Integration

## Overview
Successfully integrated comprehensive Indian food nutrition data from `Indian_Food_Nutrition_Processed.csv` into the calorie tracker application.

## Database Details
- **Total Food Items**: 499 Indian dishes and beverages
- **Data Source**: Indian_Food_Nutrition_Processed.csv
- **Serving Size**: Standardized to 100g for consistency
- **Calories**: Rounded to nearest whole number for user-friendly display

## Categories Included

### Beverages (20+ items)
- Traditional teas and coffees
- Milkshakes and lassi
- Fruit drinks and coolers
- Hot and cold beverages

### Breakfast Items (15+ items)
- Porridges (daliya, upma, poha)
- Egg preparations
- Pancakes and cheelas

### Breads & Rotis (25+ items)
- Chapati, roti, paratha varieties
- Poori, naan, bhatura
- Stuffed breads

### South Indian (10+ items)
- Idli, dosa, uttapam
- Regional specialties

### Rice Dishes (20+ items)
- Plain and flavored rice
- Biryani and pulao varieties
- Regional rice preparations

### Dal & Lentils (25+ items)
- Various dal preparations
- Curry varieties
- Sprouted preparations

### Vegetables (50+ items)
- Dry and gravy vegetables
- Stuffed vegetables
- Regional preparations
- Paneer dishes

### Non-Vegetarian (25+ items)
- Chicken, mutton, fish curries
- Kebabs and tikkas
- Regional meat dishes

### Snacks & Fried Items (40+ items)
- Pakoras, samosas, cutlets
- Traditional snacks
- Street food items

### Sweets & Desserts (50+ items)
- Traditional sweets (burfi, ladoo)
- Halwas and kheer
- Ice creams and puddings
- Cakes and pastries

### Salads & Raitas (15+ items)
- Traditional raitas
- Mixed salads
- Curd-based preparations

### Soups (25+ items)
- Traditional and continental soups
- Clear and cream soups

### Chutneys & Condiments (10+ items)
- Traditional chutneys
- Accompaniments

## Technical Implementation

### File Structure
```
src/utils/foodDatabase.ts - Main food database
scripts/csv-to-food-database.js - Conversion script
```

### Data Format
Each food item contains:
- `id`: Unique identifier
- `name`: Food name (with Hindi/regional names in parentheses)
- `calories`: Calorie content per 100g
- `serving`: Standardized to "100g"

### Search Functionality
The existing search functionality in `FoodSearch.tsx` automatically works with the new database:
- Case-insensitive search
- Partial name matching
- Real-time filtering

## Usage Instructions

1. **Adding Food**: Click "Add Food" button in the app
2. **Search**: Type any Indian food name (English or Hindi)
3. **Select**: Choose from filtered results
4. **Portion**: Adjust serving size as needed
5. **Add**: Food gets added to daily log with calculated calories

## Examples of Searchable Items

- "chai" → Hot tea (Garam Chai)
- "dosa" → Masala dosa, Semolina dosa
- "biryani" → Mutton biryani, Vegetable biryani
- "paneer" → Various paneer dishes
- "dal" → Multiple dal varieties
- "samosa" → Potato samosa, other varieties

## Benefits

1. **Comprehensive Coverage**: 499+ Indian food items
2. **Accurate Nutrition**: Based on processed nutrition data
3. **User-Friendly**: Hindi names included for easy recognition
4. **Standardized**: Consistent 100g serving size for easy calculation
5. **Searchable**: Easy to find specific dishes

## Custom Food Feature

### Overview
Added comprehensive custom food management functionality that allows users to:
- Add foods not available in the database
- Manage their custom food collection
- Export/import custom foods for backup and sharing

### Features

#### 1. **Add Custom Foods**
- **Location**: Available in Food Search modal and Custom Foods page
- **Validation**: Ensures accurate data entry with form validation
- **Duplicate Prevention**: Checks for existing food names
- **Auto-Integration**: Custom foods automatically appear in search results

#### 2. **Custom Food Management Page**
- **Access**: Click "Custom Foods" button on main page
- **Full Management**: View, add, edit, and delete custom foods
- **Statistics**: Shows count of custom foods
- **Date Tracking**: Displays when each custom food was added

#### 3. **Search Integration**
- **Unified Search**: Custom foods appear alongside database foods
- **Visual Distinction**: Custom foods have "Custom" badges
- **Delete Option**: Quick delete button for custom foods in search
- **Real-time Updates**: Search results update immediately after adding/removing

#### 4. **Data Persistence**
- **Local Storage**: Custom foods saved in browser's local storage
- **Automatic Backup**: Data persists across browser sessions
- **Export Feature**: Download custom foods as JSON file
- **Import Feature**: Upload and restore custom foods from JSON

#### 5. **User Experience**
- **Form Validation**: Real-time validation with helpful error messages
- **Success Feedback**: Clear confirmation when foods are added
- **Intuitive Interface**: Easy-to-use forms and management interface
- **Tips and Guidance**: Built-in help text for accurate data entry

### Technical Implementation

#### File Structure
```
src/utils/customFoodManager.ts - Core custom food logic
src/components/CustomFoodForm.tsx - Add custom food form
src/components/CustomFoodManager.tsx - Management interface
src/pages/CustomFoods.tsx - Dedicated custom foods page
src/utils/foodDatabase.ts - Updated to include custom foods
```

#### Data Flow
1. **Adding**: CustomFoodForm → customFoodManager → localStorage
2. **Retrieving**: getAllFoods() → combines base + custom foods
3. **Searching**: FoodSearch uses combined food list
4. **Managing**: CustomFoodManager provides full CRUD operations

#### Storage Format
```json
{
  "id": 10001,
  "name": "Homemade Pizza",
  "calories": 280,
  "serving": "1 slice",
  "isCustom": true,
  "dateAdded": "2024-01-15T10:30:00.000Z"
}
```

### Usage Guide

#### Adding Custom Foods
1. **From Food Search**:
   - Click "Add Food" → "Add Custom Food"
   - Fill in food details and submit
   - Food immediately available for selection

2. **From Custom Foods Page**:
   - Click "Custom Foods" → "Add Custom Food"
   - Complete form and save
   - View in management interface

#### Managing Custom Foods
1. **Access Management**: Click "Custom Foods" on main page
2. **View All**: See complete list with details and dates
3. **Delete**: Click trash icon to remove custom foods
4. **Export**: Download backup of all custom foods
5. **Import**: Upload previously exported custom foods

#### Best Practices
- **Accurate Calories**: Use nutrition labels or reliable sources
- **Specific Serving Sizes**: Be precise (e.g., "1 medium slice" vs "1 slice")
- **Descriptive Names**: Use clear, identifiable names
- **Regular Backups**: Export custom foods periodically

### Benefits

1. **Personalization**: Add family recipes and local foods
2. **Completeness**: Fill gaps in the existing database
3. **Accuracy**: Use exact nutrition information you trust
4. **Portability**: Export/import for backup and sharing
5. **Integration**: Seamless experience with existing features

## Daily Calorie Goal Settings

### Overview
Added comprehensive daily calorie goal management functionality that allows users to:
- Set personalized daily calorie targets
- Choose from recommended goals based on common profiles
- Validate goals for health and safety
- Export/import settings for backup and sharing

### Features

#### 1. **Interactive Goal Setting**
- **Quick Edit**: Click on the "Daily Goal" card on main page for instant editing
- **Dedicated Settings Page**: Full settings interface at `/settings`
- **Real-time Validation**: Ensures goals are within healthy ranges (800-10,000 calories)
- **Instant Updates**: Changes reflect immediately across the application

#### 2. **Recommended Goals**
- **Pre-defined Options**: 8 common calorie goal profiles
- **Gender-specific**: Separate recommendations for men and women
- **Activity-based**: Options for sedentary, moderate, and athletic lifestyles
- **Goal-oriented**: Weight loss, maintenance, and weight gain options

#### 3. **Settings Management**
- **Persistent Storage**: Goals saved in browser's localStorage
- **Export/Import**: Backup and restore settings via JSON files
- **Reset Option**: Return to default 2000-calorie goal
- **Last Updated Tracking**: Shows when settings were last modified

#### 4. **User Interface**
- **Visual Feedback**: Success/error messages for all actions
- **Progress Tracking**: Updated progress bars and remaining calories
- **Responsive Design**: Works on desktop and mobile devices
- **Accessibility**: Proper labels and keyboard navigation

### Technical Implementation

#### File Structure
```
src/utils/settingsManager.ts - Core settings management logic
src/components/CalorieGoalSettings.tsx - Goal setting interface
src/components/CalorieStats.tsx - Updated with clickable goal card
src/pages/Settings.tsx - Dedicated settings page
src/pages/Index.tsx - Updated with goal management integration
```

#### Data Flow
1. **Loading**: getUserSettings() → loads from localStorage
2. **Updating**: updateDailyCalorieGoal() → saves to localStorage
3. **Validation**: validateCalorieGoal() → ensures safe ranges
4. **Display**: Real-time updates across all components

#### Storage Format
```json
{
  "dailyCalorieGoal": 2000,
  "lastUpdated": "2024-01-15T10:30:00.000Z"
}
```

### Usage Guide

#### Setting Your Goal
1. **Quick Method**:
   - Click on "Daily Goal" card on main page
   - Enter new goal and click "Save"
   - Goal updates immediately

2. **Full Settings**:
   - Click "Settings" button on main page
   - Use comprehensive settings interface
   - Choose from recommended goals or enter custom value

#### Recommended Goals
- **Weight Loss (Women)**: 1,200 calories
- **Weight Loss (Men)**: 1,500 calories
- **Maintenance (Women)**: 2,000 calories
- **Maintenance (Men)**: 2,500 calories
- **Weight Gain (Women)**: 2,300 calories
- **Weight Gain (Men)**: 3,000 calories
- **Athletic (Women)**: 2,800 calories
- **Athletic (Men)**: 3,500 calories

#### Data Management
1. **Export Settings**: Download backup of your preferences
2. **Import Settings**: Restore from previously exported file
3. **Reset**: Return all settings to default values

### Validation Rules

#### Safety Limits
- **Minimum**: 800 calories (very low calorie diets need medical supervision)
- **Maximum**: 10,000 calories (prevents unrealistic goals)
- **Format**: Whole numbers only (no decimals)
- **Required**: Goal cannot be empty or zero

#### Health Considerations
- Goals below 1,200 calories show additional warnings
- Recommendations include activity level considerations
- Tips provided for healthy goal setting
- Encourages consultation with healthcare providers

### Benefits

1. **Personalization**: Tailored goals based on individual needs
2. **Flexibility**: Easy to adjust as circumstances change
3. **Guidance**: Recommended goals for common scenarios
4. **Safety**: Validation prevents unhealthy extremes
5. **Persistence**: Goals saved and restored automatically

### Integration with Existing Features

#### Progress Tracking
- **Real-time Updates**: Progress bars reflect new goals immediately
- **Remaining Calories**: Calculations update with goal changes
- **Visual Indicators**: Color-coded progress (green/red for over/under)

#### Food Logging
- **Consistent Experience**: All calorie calculations use current goal
- **Daily Summaries**: Show progress toward personalized target
- **Historical Data**: Previous days maintain their original goals

## Future Enhancements

- Add more nutritional information (protein, carbs, fats) for custom foods
- Include regional variations
- Add food images for custom foods
- Implement favorites system
- Add meal planning features
- Cloud sync for custom foods and settings across devices
- Community sharing of custom foods
- Weekly/monthly calorie goal trends and analytics
- Integration with fitness trackers for activity-based goal adjustments
- Macro nutrient goal setting (protein, carbs, fats percentages)

## Data Source Attribution
- **Base Database**: Indian_Food_Nutrition_Processed.csv containing comprehensive nutrition information for Indian cuisine
- **Custom Foods**: User-generated content stored locally with export/import capabilities
- **Settings**: User preferences stored locally with backup/restore functionality
