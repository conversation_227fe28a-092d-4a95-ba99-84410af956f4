import { useState, useEffect } from 'react';
import { Trash2, Download, Upload, Plus, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  getCustomFoods, 
  removeCustomFood, 
  exportCustomFoods, 
  importCustomFoods,
  type CustomFood 
} from '@/utils/customFoodManager';
import CustomFoodForm from './CustomFoodForm';
import { getAllFoods } from '@/utils/foodDatabase';

const CustomFoodManager = () => {
  const [customFoods, setCustomFoods] = useState<CustomFood[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [importMessage, setImportMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadCustomFoods();
  }, []);

  const loadCustomFoods = () => {
    setCustomFoods(getCustomFoods());
  };

  const handleDeleteFood = (id: number) => {
    if (confirm('Are you sure you want to delete this custom food?')) {
      removeCustomFood(id);
      loadCustomFoods();
    }
  };

  const handleExport = () => {
    const data = exportCustomFoods();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `custom-foods-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      const result = importCustomFoods(content);
      
      setImportMessage({
        type: result.success ? 'success' : 'error',
        text: result.message
      });

      if (result.success) {
        loadCustomFoods();
      }

      // Clear message after 5 seconds
      setTimeout(() => setImportMessage(null), 5000);
    };
    reader.readAsText(file);
    
    // Reset input
    event.target.value = '';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleFoodAdded = () => {
    loadCustomFoods();
    setShowAddForm(false);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-gray-900 dark:text-gray-100">
            <span>Custom Foods Manager</span>
            <Badge variant="secondary" className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">{customFoods.length} custom foods</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {importMessage && (
            <Alert className={`mb-4 ${importMessage.type === 'success' ? 'border-green-200 bg-green-50 dark:border-green-700 dark:bg-green-900/20' : 'border-red-200 bg-red-50 dark:border-red-700 dark:bg-red-900/20'}`}>
              <AlertDescription className={importMessage.type === 'success' ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'}>
                {importMessage.text}
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-wrap gap-2 mb-6">
            <Button
              onClick={() => setShowAddForm(true)}
              className="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Custom Food
            </Button>
            
            <Button
              onClick={handleExport}
              variant="outline"
              disabled={customFoods.length === 0}
            >
              <Download className="mr-2 h-4 w-4" />
              Export Foods
            </Button>
            
            <div className="relative">
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                id="import-file"
              />
              <Button variant="outline" asChild>
                <label htmlFor="import-file" className="cursor-pointer">
                  <Upload className="mr-2 h-4 w-4" />
                  Import Foods
                </label>
              </Button>
            </div>
          </div>

          {showAddForm && (
            <div className="mb-6 p-4 border rounded-lg bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600">
              <CustomFoodForm
                onFoodAdded={handleFoodAdded}
                onCancel={() => setShowAddForm(false)}
                existingFoods={getAllFoods()}
              />
            </div>
          )}

          {customFoods.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>No custom foods added yet.</p>
              <p className="text-sm">Click "Add Custom Food" to get started!</p>
            </div>
          ) : (
            <div className="space-y-3">
              {customFoods.map((food) => (
                <div
                  key={food.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">{food.name}</h3>
                      <Badge variant="secondary" className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                        Custom
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {food.calories} cal per {food.serving}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 flex items-center mt-1">
                      <Calendar className="mr-1 h-3 w-3" />
                      Added on {formatDate(food.dateAdded)}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-right mr-4">
                      <div className="font-bold text-blue-600 dark:text-blue-400">{food.calories}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">calories</div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                      onClick={() => handleDeleteFood(food.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-lg text-gray-900 dark:text-gray-100">Tips for Custom Foods</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
          <div>
            <strong className="text-gray-900 dark:text-gray-100">Accurate Calories:</strong> Use nutrition labels, food packaging, or reliable nutrition websites to get accurate calorie information.
          </div>
          <div>
            <strong className="text-gray-900 dark:text-gray-100">Serving Sizes:</strong> Be specific with serving sizes (e.g., "1 slice", "100g", "1 cup") for better tracking.
          </div>
          <div>
            <strong className="text-gray-900 dark:text-gray-100">Export/Import:</strong> Use export to backup your custom foods and import to restore them or share with others.
          </div>
          <div>
            <strong className="text-gray-900 dark:text-gray-100">Naming:</strong> Use descriptive names that help you identify the food easily (e.g., "Mom's Homemade Pizza" instead of just "Pizza").
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomFoodManager;
