import { useState, useEffect } from 'react';
import { Calendar, TrendingUp, BarChart3, Clock } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  getRecentDailySummaries, 
  getWeeklyStats, 
  getTodayDateString,
  type DailyLogSummary,
  type WeeklyStats 
} from '@/utils/dailyLogManager';

interface DailyStatsCardProps {
  totalCalories: number;
  totalProtein: number;
  dailyGoal: number;
}

const DailyStatsCard = ({ totalCalories, totalProtein, dailyGoal }: DailyStatsCardProps) => {
  const [recentSummaries, setRecentSummaries] = useState<DailyLogSummary[]>([]);
  const [weeklyStats, setWeeklyStats] = useState<WeeklyStats | null>(null);
  const [showHistory, setShowHistory] = useState(false);

  useEffect(() => {
    // Load recent summaries and weekly stats
    const summaries = getRecentDailySummaries(7);
    const stats = getWeeklyStats();
    
    setRecentSummaries(summaries);
    setWeeklyStats(stats);
  }, [totalCalories]); // Refresh when today's calories change

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (dateString === getTodayDateString()) {
      return 'Today';
    } else if (dateString === yesterday.toISOString().split('T')[0]) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  const getCalorieStatus = (calories: number, goal: number): { color: string; status: string } => {
    const percentage = (calories / goal) * 100;
    
    if (percentage < 80) {
      return { color: 'text-orange-600', status: 'Under Goal' };
    } else if (percentage <= 110) {
      return { color: 'text-green-600', status: 'On Target' };
    } else {
      return { color: 'text-red-600', status: 'Over Goal' };
    }
  };

  return (
    <Card className="shadow-lg border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5 text-blue-500 dark:text-blue-400" />
            <span className="text-gray-900 dark:text-gray-100">Daily Statistics</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHistory(!showHistory)}
            className="text-xs"
          >
            {showHistory ? 'Hide' : 'Show'} History
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Today's Summary */}
        <div className="p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-blue-900 dark:text-blue-100">Today's Progress</h3>
            <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100">
              <Clock className="mr-1 h-3 w-3" />
              {formatDate(getTodayDateString())}
            </Badge>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-700 dark:text-blue-300">Calories:</span>
              <div className="font-bold text-blue-900 dark:text-blue-100">{totalCalories} cal</div>
            </div>
            <div>
              <span className="text-blue-700 dark:text-blue-300">Goal:</span>
              <div className="font-bold text-blue-900 dark:text-blue-100">{dailyGoal} cal</div>
            </div>
            <div>
              <span className="text-blue-700 dark:text-blue-300">Protein:</span>
              <div className="font-bold text-orange-600 dark:text-orange-400">{totalProtein.toFixed(1)}g</div>
            </div>
            <div>
              <span className="text-blue-700 dark:text-blue-300">Status:</span>
              <span className={`font-medium ${getCalorieStatus(totalCalories, dailyGoal).color}`}>
                {getCalorieStatus(totalCalories, dailyGoal).status}
              </span>
            </div>
          </div>
        </div>

        {/* Weekly Stats */}
        {weeklyStats && (
          <div className="p-4 bg-green-50 dark:bg-green-900/30 rounded-lg">
            <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">Weekly Overview</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-green-700 dark:text-green-300">Avg Calories:</span>
                <div className="font-bold text-green-900 dark:text-green-100">{weeklyStats.averageCalories} cal/day</div>
              </div>
              <div>
                <span className="text-green-700 dark:text-green-300">Avg Protein:</span>
                <div className="font-bold text-orange-600 dark:text-orange-400">{weeklyStats.averageProtein}g/day</div>
              </div>
              <div>
                <span className="text-green-700 dark:text-green-300">Days Tracked:</span>
                <div className="font-bold text-green-900 dark:text-green-100">{weeklyStats.totalDays} days</div>
              </div>
              <div>
                <span className="text-green-700 dark:text-green-300">Highest:</span>
                <div className="font-bold text-green-900 dark:text-green-100">{weeklyStats.highestDay.calories} cal</div>
              </div>
            </div>
          </div>
        )}

        {/* Recent History */}
        {showHistory && recentSummaries.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium text-gray-900 dark:text-gray-100 flex items-center">
              <Calendar className="mr-2 h-4 w-4" />
              Recent Days
            </h3>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {recentSummaries.map((summary) => (
                <div
                  key={summary.date}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                >
                  <div>
                    <div className="font-medium text-gray-800 dark:text-gray-200">
                      {formatDate(summary.date)}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {summary.entryCount} {summary.entryCount === 1 ? 'entry' : 'entries'}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-gray-800 dark:text-gray-200">
                      {summary.totalCalories} cal
                    </div>
                    <div className="font-bold text-orange-600 dark:text-orange-400 text-sm">
                      {(summary.totalProtein || 0).toFixed(1)}g protein
                    </div>
                    <div className={`text-xs ${getCalorieStatus(summary.totalCalories, dailyGoal).color}`}>
                      {getCalorieStatus(summary.totalCalories, dailyGoal).status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No History Message */}
        {showHistory && recentSummaries.length === 0 && (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            <Calendar className="mx-auto h-8 w-8 mb-2 text-gray-400 dark:text-gray-500" />
            <p className="text-sm">No historical data available yet.</p>
            <p className="text-xs">Keep tracking to see your progress!</p>
          </div>
        )}

        {/* Streak Information */}
        {recentSummaries.length > 0 && (
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-yellow-800 dark:text-yellow-200 font-medium">Tracking Streak</span>
                <div className="text-xs text-yellow-700 dark:text-yellow-300">
                  Keep logging daily to build your streak!
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-yellow-900 dark:text-yellow-100">
                  {recentSummaries.length} {recentSummaries.length === 1 ? 'day' : 'days'}
                </div>
                <div className="text-xs text-yellow-700 dark:text-yellow-300">this week</div>
              </div>
            </div>
          </div>
        )}

        {/* Tips */}
        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">💡 Daily Tracking Tips</h4>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <li>• Log foods immediately after eating for accuracy</li>
            <li>• Your data automatically resets each day at midnight</li>
            <li>• Historical data is saved for progress tracking</li>
            <li>• Aim for consistency rather than perfection</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default DailyStatsCard;
