
import { getCustomFoods, type FoodItem } from './customFoodManager';

// Indian Food Nutrition Database
// Data source: Indian_Food_Nutrition_Processed.csv
export const baseFoodDatabase = [
  { id: 1, name: "Hot tea (Garam <PERSON>)", calories: 16, serving: "100g" },
  { id: 2, name: "Instant coffee", calories: 23, serving: "100g" },
  { id: 3, name: "Espreso coffee", calories: 52, serving: "100g" },
  { id: 4, name: "Iced tea", calories: 10, serving: "100g" },
  { id: 5, name: "Raw mango drink (Aam panna)", calories: 36, serving: "100g" },
  { id: 6, name: "Fruit Punch (with fresh juices)", calories: 36, serving: "100g" },
  { id: 7, name: "Fruit Punch (with squashes)", calories: 23, serving: "100g" },
  { id: 8, name: "Lemonade", calories: 21, serving: "100g" },
  { id: 9, name: "Lem-o-gin", calories: 22, serving: "100g" },
  { id: 10, name: "Cumin infused water (<PERSON><PERSON>/Zeere ka pani)", calories: 9, serving: "100g" },
  { id: 11, name: "Coco pine cooler", calories: 33, serving: "100g" },
  { id: 12, name: "Summer cooler", calories: 22, serving: "100g" },
  { id: 13, name: "Hot cocoa", calories: 90, serving: "100g" },
  { id: 14, name: "Cold coffee with ice cream", calories: 68, serving: "100g" },
  { id: 15, name: "Banana milkshake (Kele milkshake)", calories: 65, serving: "100g" },
  { id: 16, name: "Mango milkshake (Aam milkshake)", calories: 57, serving: "100g" },
  { id: 17, name: "Pineapple milkshake (Ananas milkshake)", calories: 56, serving: "100g" },
  { id: 18, name: "Orange milkshake (Narangi milkshake)", calories: 57, serving: "100g" },
  { id: 19, name: "Egg nog", calories: 97, serving: "100g" },
  { id: 20, name: "Sweet Lassi (Meethi lassi)", calories: 36, serving: "100g" },
  { id: 21, name: "Lassi (salted)", calories: 19, serving: "100g" },
  { id: 22, name: "Cheese and chilli sandwich ", calories: 218, serving: "100g" },
  { id: 23, name: "Egg sandwich (Ande ka sandwich)", calories: 286, serving: "100g" },
  { id: 24, name: "Cucumber sandwich (Kheere ka sandwich)", calories: 189, serving: "100g" },
  { id: 25, name: "Cheese and pineapple sandwich (Cheese aur ananas ka sandwich)", calories: 258, serving: "100g" },
  { id: 26, name: "Cheese and tomato sandwich (Cheese aur tamatar ke sandwich)", calories: 243, serving: "100g" },
  { id: 27, name: "Chicken sandwich", calories: 253, serving: "100g" },
  { id: 28, name: "Peanut and tomato sandwich (Moongfali aur tamatar ka sandwich)", calories: 291, serving: "100g" },
  { id: 29, name: "Rainbow sandwich", calories: 273, serving: "100g" },
  { id: 30, name: "Club sandwich ", calories: 235, serving: "100g" },
  { id: 31, name: "Vegetarian club sandwich", calories: 198, serving: "100g" },
  { id: 32, name: "Pin wheel sandwich", calories: 312, serving: "100g" },
  { id: 33, name: "Carrot apple sandwich (Gajar aur seb ka sandwich)", calories: 214, serving: "100g" },
  { id: 34, name: "Salami sandwich", calories: 256, serving: "100g" },
  { id: 35, name: "Vegetable and mayonnaise sandwich", calories: 244, serving: "100g" },
  { id: 36, name: "Egg and tomato sandwich (Ande aur tamatar ka sandwich)", calories: 222, serving: "100g" },
  { id: 37, name: "Sweet open sandwich", calories: 244, serving: "100g" },
  { id: 38, name: "Mushroom and cheese sandwich (toasted)", calories: 227, serving: "100g" },
  { id: 39, name: "Cheese and tomato sandwich (toasted) (Cheese aur tamatar ke sandwich (toasted))", calories: 225, serving: "100g" },
  { id: 40, name: "Pea potato sandwich (toasted) (Matar aloo ka sandwich)", calories: 165, serving: "100g" },
  { id: 41, name: "Paneer pea sandwich (toasted) (Paneer matar ka sandwich)", calories: 250, serving: "100g" },
  { id: 42, name: "Chicken sandwich (toasted)", calories: 167, serving: "100g" },
  { id: 43, name: "Pea keema sandwich (toasted) (Matar aur keema ka sandwich)", calories: 172, serving: "100g" },
  { id: 44, name: "Classic club sandwich", calories: 201, serving: "100g" },
  { id: 45, name: "Sesame toast", calories: 495, serving: "100g" },
  { id: 46, name: "Cracked wheat porridge (Meetha daliya)", calories: 82, serving: "100g" },
  { id: 47, name: "Semolina porridge (Suji/Rava daliya)", calories: 101, serving: "100g" },
  { id: 48, name: "Oatmeal Porridge", calories: 73, serving: "100g" },
  { id: 49, name: "Cornflakes with milk", calories: 117, serving: "100g" },
  { id: 50, name: "Rice flakes (Chiwda/Aval)", calories: 112, serving: "100g" },
  { id: 51, name: "Wheat flakes", calories: 112, serving: "100g" },
  { id: 52, name: "Murmura (Puffed rice)", calories: 113, serving: "100g" },
  { id: 53, name: "Puffed wheat (Murmure/Moori)", calories: 113, serving: "100g" },
  { id: 54, name: "Boiled egg (Ubla anda)", calories: 45, serving: "100g" },
  { id: 55, name: "Fried Egg ", calories: 224, serving: "100g" },
  { id: 56, name: "Poached egg", calories: 124, serving: "100g" },
  { id: 57, name: "Scrambled egg (Ande ki bhurji)", calories: 156, serving: "100g" },
  { id: 58, name: "Baked egg ", calories: 219, serving: "100g" },
  { id: 59, name: "Plain omelette/omlet", calories: 272, serving: "100g" },
  { id: 60, name: "Stuffed egg omelette/omlet", calories: 204, serving: "100g" },
  { id: 61, name: "Pancake", calories: 203, serving: "100g" },
  { id: 62, name: "Keema pancake", calories: 176, serving: "100g" },
  { id: 63, name: "Vegetable pancake", calories: 125, serving: "100g" },
  { id: 64, name: "Jam and fruit pancake", calories: 162, serving: "100g" },
  { id: 65, name: "Khoa and coconut pancake", calories: 272, serving: "100g" },
  { id: 66, name: "Brown stock", calories: 21, serving: "100g" },
  { id: 67, name: "Vegetable stock", calories: 18, serving: "100g" },
  { id: 68, name: "Chicken stock", calories: 30, serving: "100g" },
  { id: 69, name: "Clear tomato soup (Tamatar ka soup)", calories: 80, serving: "100g" },
  { id: 70, name: "Lentil soup", calories: 31, serving: "100g" },
  { id: 71, name: "Chicken consomme (Clear chicken soup)", calories: 48, serving: "100g" },
  { id: 72, name: "Cream of tomato soup", calories: 98, serving: "100g" },
  { id: 73, name: "Cream of green peas soup", calories: 128, serving: "100g" },
  { id: 74, name: "Cream of spinach soup", calories: 101, serving: "100g" },
  { id: 75, name: "Cream of mixed vegetable soup", calories: 60, serving: "100g" },
  { id: 76, name: "Cream of mushroom soup", calories: 117, serving: "100g" },
  { id: 77, name: "Chicken sweet corn soup", calories: 28, serving: "100g" },
  { id: 78, name: "Minestrone soup", calories: 43, serving: "100g" },
  { id: 79, name: "Egg drop soup", calories: 27, serving: "100g" },
  { id: 80, name: "Chinese cabbage and meat ball soup", calories: 484, serving: "100g" },
  { id: 81, name: "French onion soup", calories: 56, serving: "100g" },
  { id: 82, name: "Hot and sour soup", calories: 32, serving: "100g" },
  { id: 83, name: "Talaumein soup", calories: 36, serving: "100g" },
  { id: 84, name: "Cold cucumber soup (Thanda kheere ka soup)", calories: 48, serving: "100g" },
  { id: 85, name: "Cold summer garden soup", calories: 49, serving: "100g" },
  { id: 86, name: "Chapati/Roti", calories: 202, serving: "100g" },
  { id: 87, name: "Plain parantha/paratha", calories: 298, serving: "100g" },
  { id: 88, name: "Potato parantha/paratha (Aloo ka parantha/paratha)", calories: 205, serving: "100g" },
  { id: 89, name: "Radish parantha/paratha (Mooli ka parantha/paratha)", calories: 184, serving: "100g" },
  { id: 90, name: "Cauliflower parantha/paratha (Phoolgobhi ka parantha/paratha)", calories: 178, serving: "100g" },
  { id: 91, name: "Dal parantha/paratha", calories: 268, serving: "100g" },
  { id: 92, name: "Sprouted moong parantha/paratha", calories: 229, serving: "100g" },
  { id: 93, name: "Pea parantha/paratha (Matar ka parantha/paratha)", calories: 191, serving: "100g" },
  { id: 94, name: "Keema parantha/paratha", calories: 238, serving: "100g" },
  { id: 95, name: "Paneer parantha/paratha", calories: 263, serving: "100g" },
  { id: 96, name: "Besan and spinach parantha/paratha (Besan aur palak ka parantha/paratha)", calories: 216, serving: "100g" },
  { id: 97, name: "Poori", calories: 738, serving: "100g" },
  { id: 98, name: "Spinach poori (Palak poori)", calories: 684, serving: "100g" },
  { id: 99, name: "Methi poori", calories: 710, serving: "100g" },
  { id: 100, name: "Dal stuffed poori", calories: 785, serving: "100g" },
  { id: 101, name: "Potato stuffed poori (Aloo ki poori)", calories: 777, serving: "100g" },
  { id: 102, name: "Tandoori parantha/paratha", calories: 295, serving: "100g" },
  { id: 103, name: "Boiled rice (Uble chawal)", calories: 117, serving: "100g" },
  { id: 104, name: "Plain pulao", calories: 140, serving: "100g" },
  { id: 105, name: "Mixed vegetable pulao", calories: 113, serving: "100g" },
  { id: 106, name: "Mushroom pulao", calories: 124, serving: "100g" },
  { id: 107, name: "Sprouted moong pulao", calories: 113, serving: "100g" },
  { id: 108, name: "Paneer pulao", calories: 582, serving: "100g" },
  { id: 109, name: "Peanut pulao ", calories: 193, serving: "100g" },
  { id: 110, name: "Navratan pulao", calories: 262, serving: "100g" },
  { id: 111, name: "Green chickpeas pulao (Choliya pulao/Hare chane ka pulao)", calories: 173, serving: "100g" },
  { id: 112, name: "Mutton biryani/biriyani", calories: 191, serving: "100g" },
  { id: 113, name: "Vegetable biryani/biriyani", calories: 175, serving: "100g" },
  { id: 114, name: "Lemon rice (Pulihora, Elumichai sadam, Chitranna)", calories: 176, serving: "100g" },
  { id: 115, name: "Sweet rice (Meethe chawal)", calories: 215, serving: "100g" },
  { id: 116, name: "Curd rice (Dahi bhaat/Dahi chawal/ Perugu annam/Daddojanam/Thayir saadam)", calories: 196, serving: "100g" },
  { id: 117, name: "Tamarind rice (Chintapandu pulihora/Puliyodharai/Puli sadam/Huli anna)", calories: 373, serving: "100g" },
  { id: 118, name: "Spanish rice", calories: 164, serving: "100g" },
  { id: 119, name: "Chinese fried rice", calories: 121, serving: "100g" },
  { id: 120, name: "Macroni cheese pie", calories: 171, serving: "100g" },
  { id: 121, name: "Vegetable chowmein", calories: 130, serving: "100g" },
  { id: 122, name: "Chicken chowmein", calories: 151, serving: "100g" },
  { id: 123, name: "Cheese noodle ring", calories: 134, serving: "100g" },
  { id: 124, name: "Spaghetti and cheese balls in tomato sauce", calories: 508, serving: "100g" },
  { id: 125, name: "Penne platter", calories: 177, serving: "100g" },
  { id: 126, name: "Pasta hot pot", calories: 125, serving: "100g" },
  { id: 127, name: "Chicken lasagne", calories: 187, serving: "100g" },
  { id: 128, name: "Fettuccine with spinach sauce", calories: 129, serving: "100g" },
  { id: 129, name: "Naan", calories: 286, serving: "100g" },
  { id: 130, name: "Bhatura", calories: 793, serving: "100g" },
  { id: 131, name: "Idli", calories: 138, serving: "100g" },
  { id: 132, name: "Masala dosa", calories: 165, serving: "100g" },
  { id: 133, name: "Semolina dosa (Suji/Rava dosa)", calories: 227, serving: "100g" },
  { id: 134, name: "Onion tomato uttapam", calories: 462, serving: "100g" },
  { id: 135, name: "Paneer kaathi roll", calories: 286, serving: "100g" },
  { id: 136, name: "Makki ki roti", calories: 264, serving: "100g" },
  { id: 137, name: "Washed moong dal (Dhuli moong ki dal)", calories: 50, serving: "100g" },
  { id: 138, name: "Washed urad dal (Dhuli urad ki dal)", calories: 61, serving: "100g" },
  { id: 139, name: "Split bengal gram with bottle gourd (Channa dal with ghiya/lauki)", calories: 73, serving: "100g" },
  { id: 140, name: "Dry washed urad", calories: 125, serving: "100g" },
  { id: 141, name: "Mixed dal", calories: 62, serving: "100g" },
  { id: 142, name: "Whole moong (Moong ki dal)", calories: 54, serving: "100g" },
  { id: 143, name: "Whole masoor (Masoor ki dal)", calories: 54, serving: "100g" },
  { id: 144, name: "Whole moth (Moth ki dal)", calories: 55, serving: "100g" },
  { id: 145, name: "Whole urad (Urad ki dal)", calories: 54, serving: "100g" },
  { id: 146, name: "Moti mahal dal (Urad rajmah mix dal)", calories: 103, serving: "100g" },
  { id: 147, name: "Black channa curry/Bengal gram curry (Kale chane ki curry)", calories: 141, serving: "100g" },
  { id: 148, name: "Chickpeas curry (Safed channa curry)", calories: 163, serving: "100g" },
  { id: 149, name: "Lobia curry", calories: 149, serving: "100g" },
  { id: 150, name: "Soyabean curry", calories: 163, serving: "100g" },
  { id: 151, name: "Kidney bean curry (Rajmah curry)", calories: 144, serving: "100g" },
  { id: 152, name: "Sambar", calories: 97, serving: "100g" },
  { id: 153, name: "Besan kadhi with pakodies", calories: 403, serving: "100g" },
  { id: 154, name: "Khatta channa", calories: 203, serving: "100g" },
  { id: 155, name: "Sprouted moong dal chat", calories: 32, serving: "100g" },
  { id: 156, name: "Potato cauliflower (Aloo gobhi)", calories: 106, serving: "100g" },
  { id: 157, name: "Potato capsicum (Shimla mirch aloo)", calories: 126, serving: "100g" },
  { id: 158, name: "Cabbage and peas (Pattagobhi aur matar)", calories: 64, serving: "100g" },
  { id: 159, name: "Carrot and fenugreek leaves (Gajar methi)", calories: 62, serving: "100g" },
  { id: 160, name: "Potato fenugreek (Aloo methi)", calories: 135, serving: "100g" },
  { id: 161, name: "Brinjal bhartha (Baingan ka bhartha)", calories: 65, serving: "100g" },
  { id: 162, name: "Dry potato (Sookhe aloo)", calories: 103, serving: "100g" },
  { id: 163, name: "Beans with coconut (Nariyal aur sem/phali; Beans thoran)", calories: 132, serving: "100g" },
  { id: 164, name: "Cauliflower with coconut (Nariyal ke saath phoolgobhi)", calories: 132, serving: "100g" },
  { id: 165, name: "Carrot and cabbage with coconut (Nariyal ke saath pattagobhi aur gajar)", calories: 107, serving: "100g" },
  { id: 166, name: "Raw turnip with coconut ", calories: 132, serving: "100g" },
  { id: 167, name: "Raw papaya with coconut (Papaya thoran)", calories: 133, serving: "100g" },
  { id: 168, name: "Stuffed okra (Bharwa bhindi)", calories: 94, serving: "100g" },
  { id: 169, name: "Stuffed round gourd (Bharwa tinde)", calories: 73, serving: "100g" },
  { id: 170, name: "Stuffed capsicum (Bharwa shimla mirch)", calories: 91, serving: "100g" },
  { id: 171, name: "Stuffed brinjal (Bharwa baingan)", calories: 93, serving: "100g" },
  { id: 172, name: "Stuffed tomatoes (Bharwa tamatar)", calories: 93, serving: "100g" },
  { id: 173, name: "Pea potato curry (Aloo matar)", calories: 101, serving: "100g" },
  { id: 174, name: "Pea paneer curry (Matar paneer)", calories: 135, serving: "100g" },
  { id: 175, name: "Pea mushroom curry (Matar mushroom)", calories: 93, serving: "100g" },
  { id: 176, name: "Pea curry (Matar ki sabzi)", calories: 103, serving: "100g" },
  { id: 177, name: "Pea vadi curry", calories: 102, serving: "100g" },
  { id: 178, name: "Paneer curry", calories: 177, serving: "100g" },
  { id: 179, name: "Lotus stem curry (Kamal kakdi curry)", calories: 110, serving: "100g" },
  { id: 180, name: "Gravy for kofta", calories: 126, serving: "100g" },
  { id: 181, name: "Pea kofta curry (Matar kofta curry)", calories: 596, serving: "100g" },
  { id: 182, name: "Spinach kofta curry (Palak kofta curry)", calories: 572, serving: "100g" },
  { id: 183, name: "Paneer kofta curry", calories: 671, serving: "100g" },
  { id: 184, name: "Lotus stem kofta curry (Kamal kakdi kofta curry)", calories: 634, serving: "100g" },
  { id: 185, name: "Raw banana kofta curry (Kela kofta curry)", calories: 627, serving: "100g" },
  { id: 186, name: "Cauliflower kofta curry (Phoolgobhi kofta curry)", calories: 641, serving: "100g" },
  { id: 187, name: "Cabbage kofta curry (Pattagobhi kofta curry)", calories: 640, serving: "100g" },
  { id: 188, name: "Ghiya/Lauki Kofta Curry", calories: 639, serving: "100g" },
  { id: 189, name: "Spinach paneer kofta curry (Palak paneer kofta curry)", calories: 606, serving: "100g" },
  { id: 190, name: "Vegetarian egg kofta curry", calories: 627, serving: "100g" },
  { id: 191, name: "Baked vegetables", calories: 89, serving: "100g" },
  { id: 192, name: "Cauliflower musallam (Phoolgobhi musallam)", calories: 59, serving: "100g" },
  { id: 193, name: "Baked vegetables with spinach ", calories: 78, serving: "100g" },
  { id: 194, name: "Baked potato with skin ", calories: 136, serving: "100g" },
  { id: 195, name: "Shepherd's pie (vegetarian)", calories: 136, serving: "100g" },
  { id: 196, name: "Baked brinjal in tomato sauce", calories: 39, serving: "100g" },
  { id: 197, name: "Dum aloo", calories: 682, serving: "100g" },
  { id: 198, name: "Spinach paneer (Palak paneer)", calories: 78, serving: "100g" },
  { id: 199, name: "Methi chaman", calories: 476, serving: "100g" },
  { id: 200, name: "Sarson ka saag", calories: 88, serving: "100g" },
  { id: 201, name: "Jackfruit sabzi (Kathal ki sabzi)", calories: 625, serving: "100g" },
  { id: 202, name: "Avial", calories: 125, serving: "100g" },
  { id: 203, name: "Al yakhani", calories: 148, serving: "100g" },
  { id: 204, name: "Shahi paneer", calories: 157, serving: "100g" },
  { id: 205, name: "Paneer in butter sauce", calories: 146, serving: "100g" },
  { id: 206, name: "Methi malai paneer", calories: 195, serving: "100g" },
  { id: 207, name: "Chilli paneer", calories: 778, serving: "100g" },
  { id: 208, name: "Paneer makhana korma", calories: 776, serving: "100g" },
  { id: 209, name: "Kadhai Paneer", calories: 108, serving: "100g" },
  { id: 210, name: "Roghan josh", calories: 140, serving: "100g" },
  { id: 211, name: "Spinach mutton (Palak mutton)", calories: 80, serving: "100g" },
  { id: 212, name: "Pea keema curry (Matar keema ki sabzi)", calories: 133, serving: "100g" },
  { id: 213, name: "Keema kofta curry", calories: 154, serving: "100g" },
  { id: 214, name: "Kashmiri mutton koftas (Gushtaba)", calories: 97, serving: "100g" },
  { id: 215, name: "Mutton yakhni", calories: 104, serving: "100g" },
  { id: 216, name: "Chicken yakhni", calories: 99, serving: "100g" },
  { id: 217, name: "Mutton do piaza", calories: 183, serving: "100g" },
  { id: 218, name: "Mutton chops", calories: 664, serving: "100g" },
  { id: 219, name: "Shammi kebab", calories: 686, serving: "100g" },
  { id: 220, name: "Scotch egg", calories: 677, serving: "100g" },
  { id: 221, name: "Shepherd's pie (with minced meat)", calories: 143, serving: "100g" },
  { id: 222, name: "Chicken curry", calories: 129, serving: "100g" },
  { id: 223, name: "Tandoori chicken", calories: 145, serving: "100g" },
  { id: 224, name: "Butter chicken", calories: 137, serving: "100g" },
  { id: 225, name: "Chicken kebab", calories: 729, serving: "100g" },
  { id: 226, name: "Chilli chicken", calories: 199, serving: "100g" },
  { id: 227, name: "Fried chicken with tomato sauce (Fried chicken tamatar ki chutney kay saath)", calories: 125, serving: "100g" },
  { id: 228, name: "Fish curry (Machli curry)", calories: 111, serving: "100g" },
  { id: 229, name: "Fried fish (Indian style) (Tali hui machli)", calories: 659, serving: "100g" },
  { id: 230, name: "Fried fish and Chips (English Style) (Tali hui machli aur chips)", calories: 652, serving: "100g" },
  { id: 231, name: "Tomato fish", calories: 490, serving: "100g" },
  { id: 232, name: "Baked fish with cheese sauce", calories: 114, serving: "100g" },
  { id: 233, name: "Fish tikka", calories: 117, serving: "100g" },
  { id: 234, name: "Tandoori fish", calories: 96, serving: "100g" },
  { id: 235, name: "Paneer, apple and pineapple salad", calories: 95, serving: "100g" },
  { id: 236, name: "Russian salad", calories: 115, serving: "100g" },
  { id: 237, name: "Pasta salad", calories: 190, serving: "100g" },
  { id: 238, name: "Deviled egg", calories: 99, serving: "100g" },
  { id: 239, name: "Hawain salad", calories: 175, serving: "100g" },
  { id: 240, name: "Mixed pulse and vegetable salad", calories: 83, serving: "100g" },
  { id: 241, name: "Sprouted moong salad ", calories: 38, serving: "100g" },
  { id: 242, name: "Tossed salad", calories: 63, serving: "100g" },
  { id: 243, name: "Mixed vegetable salad with curd sauce", calories: 60, serving: "100g" },
  { id: 244, name: "Potato salad (Aloo ka salaad)", calories: 90, serving: "100g" },
  { id: 245, name: "Waldroff salad", calories: 142, serving: "100g" },
  { id: 246, name: "Coleslaw", calories: 77, serving: "100g" },
  { id: 247, name: "Fruit salad (Phalon ka salaad)", calories: 78, serving: "100g" },
  { id: 248, name: "Tomato onion raita (Tamatar aur pyaaz ka raita)", calories: 59, serving: "100g" },
  { id: 249, name: "Peanut raita (Mungfali ka raita)", calories: 150, serving: "100g" },
  { id: 250, name: "Sprouted moong raita ", calories: 61, serving: "100g" },
  { id: 251, name: "Bottle gourd raita (Ghiya/Lauki ka raita)", calories: 56, serving: "100g" },
  { id: 252, name: "Cucumber raita (Kheere ka raita)", calories: 59, serving: "100g" },
  { id: 253, name: "Carrot and spinach raita (Gajar aur palak ka raita)", calories: 57, serving: "100g" },
  { id: 254, name: "Mint raita (Pudinay ka raita)", calories: 78, serving: "100g" },
  { id: 255, name: "Potato raita (Aloo ka raita)", calories: 75, serving: "100g" },
  { id: 256, name: "Boondi raita", calories: 688, serving: "100g" },
  { id: 257, name: "Sweet raita (Meetha raita)", calories: 99, serving: "100g" },
  { id: 258, name: "Dahi vadas/Dahi bhalla", calories: 668, serving: "100g" },
  { id: 259, name: "Gunjia", calories: 667, serving: "100g" },
  { id: 260, name: "Saunth/Sonth chutney with tamarind/imli", calories: 142, serving: "100g" },
  { id: 261, name: "Rice kheer (Chawal ki kheer)", calories: 75, serving: "100g" },
  { id: 262, name: "Makhana kheer", calories: 108, serving: "100g" },
  { id: 263, name: "Vermicelli kheer (Semiya/Seviyan ki kheer)", calories: 120, serving: "100g" },
  { id: 264, name: "Semolina kheer (Suji/Rava kheer)", calories: 113, serving: "100g" },
  { id: 265, name: "Paneer kheer", calories: 105, serving: "100g" },
  { id: 266, name: "Cabbage kheer (Pattagobhi ki kheer)", calories: 84, serving: "100g" },
  { id: 267, name: "Carrot kheer (Gajar ki kheer)", calories: 85, serving: "100g" },
  { id: 268, name: "Cauliflower kheer (Phoolgobhi ki kheer)", calories: 82, serving: "100g" },
  { id: 269, name: "Moong dal kheer", calories: 77, serving: "100g" },
  { id: 270, name: "Phirni", calories: 116, serving: "100g" },
  { id: 271, name: "Semolina halwa (Suji ka halwa)", calories: 226, serving: "100g" },
  { id: 272, name: "Shahi suji halwa", calories: 382, serving: "100g" },
  { id: 273, name: "Carrot halwa (Gajar ka halwa)", calories: 173, serving: "100g" },
  { id: 274, name: "Egg halwa (Ande ka halwa)", calories: 300, serving: "100g" },
  { id: 275, name: "Potato halwa (Aloo ka halwa)", calories: 152, serving: "100g" },
  { id: 276, name: "Pumpkin halwa (Kaddu ka halwa)", calories: 190, serving: "100g" },
  { id: 277, name: "Moong dal halwa", calories: 350, serving: "100g" },
  { id: 278, name: "Caramel custard (steamed)", calories: 122, serving: "100g" },
  { id: 279, name: "Baked custard", calories: 107, serving: "100g" },
  { id: 280, name: "Soft Custard (stirred)", calories: 109, serving: "100g" },
  { id: 281, name: "Chocolate ice cream", calories: 178, serving: "100g" },
  { id: 282, name: "Mango ice cream", calories: 126, serving: "100g" },
  { id: 283, name: "Fruit Ice cream (Phalon ka Ice cream)", calories: 171, serving: "100g" },
  { id: 284, name: "Caramel ice cream", calories: 174, serving: "100g" },
  { id: 285, name: "Lemon souffle", calories: 169, serving: "100g" },
  { id: 286, name: "Orange souffle", calories: 168, serving: "100g" },
  { id: 287, name: "Vanilla souffle", calories: 188, serving: "100g" },
  { id: 288, name: "Chocolate souffle", calories: 189, serving: "100g" },
  { id: 289, name: "Pineapple souffle ", calories: 196, serving: "100g" },
  { id: 290, name: "Apple mousse", calories: 107, serving: "100g" },
  { id: 291, name: "Rich chocolate mousse", calories: 188, serving: "100g" },
  { id: 292, name: "Mango mousse", calories: 125, serving: "100g" },
  { id: 293, name: "Orange and pineapple cream", calories: 101, serving: "100g" },
  { id: 294, name: "Charlotte rousse", calories: 188, serving: "100g" },
  { id: 295, name: "Triffle pudding", calories: 117, serving: "100g" },
  { id: 296, name: "Snow flakes pudding", calories: 95, serving: "100g" },
  { id: 297, name: "Kulfi", calories: 98, serving: "100g" },
  { id: 298, name: "Steamed orange pudding", calories: 278, serving: "100g" },
  { id: 299, name: "Meringue and rice pudding", calories: 113, serving: "100g" },
  { id: 300, name: "Cheese pudding", calories: 285, serving: "100g" },
  { id: 301, name: "Chocolate pudding", calories: 212, serving: "100g" },
  { id: 302, name: "Bread and butter pudding ", calories: 168, serving: "100g" },
  { id: 303, name: "Queen of pudding", calories: 133, serving: "100g" },
  { id: 304, name: "Pineapple upside down pudding", calories: 258, serving: "100g" },
  { id: 305, name: "Date and nut pie", calories: 322, serving: "100g" },
  { id: 306, name: "Stewed apple with custard", calories: 93, serving: "100g" },
  { id: 307, name: "Apple snowballs", calories: 102, serving: "100g" },
  { id: 308, name: "Hot orange souffle", calories: 180, serving: "100g" },
  { id: 309, name: "Hot chocolate souffle", calories: 226, serving: "100g" },
  { id: 310, name: "Hot vanilla souffle", calories: 193, serving: "100g" },
  { id: 311, name: "Plain burfi (Burfi)", calories: 408, serving: "100g" },
  { id: 312, name: "Coconut burfi (Nariyal ki burfi)", calories: 468, serving: "100g" },
  { id: 313, name: "Bottle gourd burfi (Ghiya/Lauki burfi)", calories: 276, serving: "100g" },
  { id: 314, name: "Chocolate burfi", calories: 339, serving: "100g" },
  { id: 315, name: "Cashewnut burfi (Kaju burfi/Kaju katli)", calories: 422, serving: "100g" },
  { id: 316, name: "Gram flour burfi (Besan burfi)", calories: 422, serving: "100g" },
  { id: 317, name: "Semolina ladoo with khoya (Suji/Rava aur khoye ke ladoo )", calories: 429, serving: "100g" },
  { id: 318, name: "Semolina ladoo with coconut (Suji/Rava aur nariyal ke ladoo )", calories: 464, serving: "100g" },
  { id: 319, name: "Gram flour ladoo (Besan ladoo)", calories: 477, serving: "100g" },
  { id: 320, name: "Sesame ladoo (Til ke ladoo)", calories: 397, serving: "100g" },
  { id: 321, name: "Chenna murki", calories: 253, serving: "100g" },
  { id: 322, name: "Milk cake", calories: 127, serving: "100g" },
  { id: 323, name: "Ghujia/Lavang latika", calories: 769, serving: "100g" },
  { id: 324, name: "Gulab Jamun with khoya", calories: 586, serving: "100g" },
  { id: 325, name: "Mal pua", calories: 567, serving: "100g" },
  { id: 326, name: "Shahi tukre", calories: 190, serving: "100g" },
  { id: 327, name: "Potato pakora/pakoda (Aloo pakoda)", calories: 677, serving: "100g" },
  { id: 328, name: "Onion pakora/pakoda (Pyaaz ke pakode)", calories: 675, serving: "100g" },
  { id: 329, name: "Cauliflower pakora/pakoda (Phoolgobhi ke pakode)", calories: 672, serving: "100g" },
  { id: 330, name: "Mixed vegetable pakora/pakoda", calories: 674, serving: "100g" },
  { id: 331, name: "Spinach pakora/pakoda (Palak pakoda)", calories: 713, serving: "100g" },
  { id: 332, name: "Methi pakora/pakoda (Methi ke pakode)", calories: 713, serving: "100g" },
  { id: 333, name: "Egg pakora/pakoda (Ande ke pakode)", calories: 711, serving: "100g" },
  { id: 334, name: "Bread pakora/pakoda", calories: 711, serving: "100g" },
  { id: 335, name: "Paneer pakora/pakoda", calories: 718, serving: "100g" },
  { id: 336, name: "Potato bonda (Aloo bonda)", calories: 633, serving: "100g" },
  { id: 337, name: "Potato samosa (Aloo ka samosa)", calories: 577, serving: "100g" },
  { id: 338, name: "Minced meat samosa (Keema ka samosa)", calories: 621, serving: "100g" },
  { id: 339, name: "Paneer and pea samosa (Paneer matar ka samosa)", calories: 624, serving: "100g" },
  { id: 340, name: "Mathri", calories: 805, serving: "100g" },
  { id: 341, name: "Khasta kachori", calories: 713, serving: "100g" },
  { id: 342, name: "Vegetable cutlet", calories: 665, serving: "100g" },
  { id: 343, name: "Flattened rice cutlet (Chirwa cutlet/Chivda cutlet/Poha cutlet)", calories: 702, serving: "100g" },
  { id: 344, name: "Peanut cutlet (Mungfali ke cutlet)", calories: 699, serving: "100g" },
  { id: 345, name: "Fish cutlet (Machli ka cutlet)", calories: 655, serving: "100g" },
  { id: 346, name: "Paneer potato cutlet (Paneer aloo cutlet)", calories: 673, serving: "100g" },
  { id: 347, name: "Spinach chickpeas cutlet (Palak channa dal cutlet)", calories: 688, serving: "100g" },
  { id: 348, name: "Cheese toast", calories: 785, serving: "100g" },
  { id: 349, name: "Vegetable burger", calories: 520, serving: "100g" },
  { id: 350, name: "Cheese pizza", calories: 250, serving: "100g" },
  { id: 351, name: "Vegetable seekh kebab", calories: 691, serving: "100g" },
  { id: 352, name: "Masala vada", calories: 826, serving: "100g" },
  { id: 353, name: "Peanut sago vada (Sabudana mungfali vada)", calories: 750, serving: "100g" },
  { id: 354, name: "Vegeterian scotch egg", calories: 682, serving: "100g" },
  { id: 355, name: "Paneer shaslik/tikka", calories: 94, serving: "100g" },
  { id: 356, name: "Peanut brittle (Moongfali ki chikki)", calories: 320, serving: "100g" },
  { id: 357, name: "Spring roll", calories: 624, serving: "100g" },
  { id: 358, name: "Dry mango chutney (Sookhe aam ki chutney)", calories: 298, serving: "100g" },
  { id: 359, name: "Peanut chutney (Mungfali ki chutney)", calories: 258, serving: "100g" },
  { id: 360, name: "Coconut chutney (Nariyal ki chutney)", calories: 266, serving: "100g" },
  { id: 361, name: "Mint and coriander chutney (Pudinay aur dhaniye ki chutney)", calories: 103, serving: "100g" },
  { id: 362, name: "Custard tart", calories: 226, serving: "100g" },
  { id: 363, name: "Lemon tart", calories: 303, serving: "100g" },
  { id: 364, name: "Jam tart", calories: 361, serving: "100g" },
  { id: 365, name: "Orange cream tart", calories: 197, serving: "100g" },
  { id: 366, name: "Pineapple tart", calories: 195, serving: "100g" },
  { id: 367, name: "Cheese and mushroom tart", calories: 246, serving: "100g" },
  { id: 368, name: "Cottage cheese pie", calories: 239, serving: "100g" },
  { id: 369, name: "Minced meat pie", calories: 222, serving: "100g" },
  { id: 370, name: "Apple cinnamon pie", calories: 259, serving: "100g" },
  { id: 371, name: "Lemon meringue pie", calories: 224, serving: "100g" },
  { id: 372, name: "Chocolate meringue pie", calories: 215, serving: "100g" },
  { id: 373, name: "Cream puffs", calories: 221, serving: "100g" },
  { id: 374, name: "Chocolate eclairs", calories: 239, serving: "100g" },
  { id: 375, name: "Cheese balls", calories: 681, serving: "100g" },
  { id: 376, name: "Minced meat patties", calories: 266, serving: "100g" },
  { id: 377, name: "Cheese patties", calories: 324, serving: "100g" },
  { id: 378, name: "Hot cheese souffle", calories: 177, serving: "100g" },
  { id: 379, name: "Hot potato souffle", calories: 126, serving: "100g" },
  { id: 380, name: "Hot fish souffle", calories: 143, serving: "100g" },
  { id: 381, name: "Hot spinach souffle", calories: 104, serving: "100g" },
  { id: 382, name: "Plain cream cake", calories: 354, serving: "100g" },
  { id: 383, name: "Apple cake (Seb ka cake)", calories: 290, serving: "100g" },
  { id: 384, name: "Marble cake", calories: 354, serving: "100g" },
  { id: 385, name: "Chocolate cake", calories: 336, serving: "100g" },
  { id: 386, name: "Orange cake", calories: 358, serving: "100g" },
  { id: 387, name: "Fruit Loaf ", calories: 324, serving: "100g" },
  { id: 388, name: "Banana cake (Kele ka cake)", calories: 390, serving: "100g" },
  { id: 389, name: "Chocolate chiffon cake", calories: 312, serving: "100g" },
  { id: 390, name: "Christmas cake", calories: 373, serving: "100g" },
  { id: 391, name: "Strawberry and vanilla cake with butter icing", calories: 371, serving: "100g" },
  { id: 392, name: "Eggless cake", calories: 318, serving: "100g" },
  { id: 393, name: "Swiss roll", calories: 252, serving: "100g" },
  { id: 394, name: "Pineapple pastry ", calories: 208, serving: "100g" },
  { id: 395, name: "Black forest pastry", calories: 234, serving: "100g" },
  { id: 396, name: "Coconut finger", calories: 275, serving: "100g" },
  { id: 397, name: "Pineapple cake ", calories: 196, serving: "100g" },
  { id: 398, name: "Chocolate walnut cookies (Chocolate aur akhrot ke cookies)", calories: 425, serving: "100g" },
  { id: 399, name: "Chocolate chip cookies", calories: 425, serving: "100g" },
  { id: 400, name: "Sweet plain biscuit", calories: 381, serving: "100g" },
  { id: 401, name: "Chocolate biscuit", calories: 380, serving: "100g" },
  { id: 402, name: "Coconut biscuit (Nariyal ke biscuit)", calories: 435, serving: "100g" },
  { id: 403, name: "Sweet and salty biscuit (Meethay aur namkeen biscuit)", calories: 408, serving: "100g" },
  { id: 404, name: "Peanut biscuit", calories: 401, serving: "100g" },
  { id: 405, name: "Pin wheel biscuit", calories: 405, serving: "100g" },
  { id: 406, name: "Saffron biscuit (Kesar biscuit)", calories: 459, serving: "100g" },
  { id: 407, name: "Masala biscuit", calories: 394, serving: "100g" },
  { id: 408, name: "Ice box cookies", calories: 374, serving: "100g" },
  { id: 409, name: "Ginger bread man", calories: 365, serving: "100g" },
  { id: 410, name: "Danish cookies", calories: 475, serving: "100g" },
  { id: 411, name: "Short bread cookies", calories: 443, serving: "100g" },
  { id: 412, name: "Coffee biscuit", calories: 417, serving: "100g" },
  { id: 413, name: "Melting moments", calories: 438, serving: "100g" },
  { id: 414, name: "Ginger biscuit (Adarak ke biscuit)", calories: 418, serving: "100g" },
  { id: 415, name: "Soyabean muthias", calories: 839, serving: "100g" },
  { id: 416, name: "Soyabean tikki", calories: 698, serving: "100g" },
  { id: 417, name: "Soyabean namak paras", calories: 838, serving: "100g" },
  { id: 418, name: "Peanut burfi (Moongfali ki burfi)", calories: 551, serving: "100g" },
  { id: 419, name: "Spinach peanut namak paras (Palak moongfali namak paras)", calories: 740, serving: "100g" },
  { id: 420, name: "Gram flour and semolina chilla/cheela/savory pancake (Besan suji chilla/cheela)", calories: 759, serving: "100g" },
  { id: 421, name: "Rice moong dal cheela (Chawal aur moong dal ka cheela)", calories: 798, serving: "100g" },
  { id: 422, name: "Split bengal gram sweet rice (Channa dal sweet rice)", calories: 190, serving: "100g" },
  { id: 423, name: "Sweet poori (Meethi poori)", calories: 783, serving: "100g" },
  { id: 424, name: "Wheat flour and moong dal burfi (Atta aur moong dal ki burfi)", calories: 441, serving: "100g" },
  { id: 425, name: "Spinach burfi (Palak burfi)", calories: 121, serving: "100g" },
  { id: 426, name: "Sweet split chickpea roti (Sweet channa dal roti/Puranpoli)", calories: 367, serving: "100g" },
  { id: 427, name: "Sprouted moong daliya ", calories: 112, serving: "100g" },
  { id: 428, name: "Sprouted moong poha ", calories: 193, serving: "100g" },
  { id: 429, name: "Pearl millet ladoo (Bajra ladoo)", calories: 320, serving: "100g" },
  { id: 430, name: "Paushtik ladoo", calories: 486, serving: "100g" },
  { id: 431, name: "Paushtik roti", calories: 149, serving: "100g" },
  { id: 432, name: "Semolina carrot vada (Suji gajar vada)", calories: 700, serving: "100g" },
  { id: 433, name: "Dhokla", calories: 216, serving: "100g" },
  { id: 434, name: "Kashmiri tea (Kehwa)", calories: 25, serving: "100g" },
  { id: 435, name: "Steeped hot coffee", calories: 16, serving: "100g" },
  { id: 436, name: "Hot chocolate", calories: 90, serving: "100g" },
  { id: 437, name: "Cold coffee (with cream)", calories: 83, serving: "100g" },
  { id: 438, name: "Banana milkshake with ice cream (Kele ka milkshake ice cream ke saath)", calories: 76, serving: "100g" },
  { id: 439, name: "Flavoured milkshake", calories: 67, serving: "100g" },
  { id: 440, name: "Jal jeera", calories: 30, serving: "100g" },
  { id: 441, name: "Gingo", calories: 48, serving: "100g" },
  { id: 442, name: "Mintade", calories: 40, serving: "100g" },
  { id: 443, name: "Canjee", calories: 7, serving: "100g" },
  { id: 444, name: "Cauliflower canjee (Phoolgobhi ki canjee)", calories: 7, serving: "100g" },
  { id: 445, name: "Potato canjee (Aloo canjee)", calories: 16, serving: "100g" },
  { id: 446, name: "Vermicelli porridge (Semiya/Seviyan porridge)", calories: 78, serving: "100g" },
  { id: 447, name: "Semolina upma (Suji/Rava upma)", calories: 148, serving: "100g" },
  { id: 448, name: "Vermicelli upma (Semiya/Seviyan upma)", calories: 149, serving: "100g" },
  { id: 449, name: "Rice upma", calories: 151, serving: "100g" },
  { id: 450, name: "Bread upma", calories: 130, serving: "100g" },
  { id: 451, name: "Vegetable upma", calories: 146, serving: "100g" },
  { id: 452, name: "Poha", calories: 295, serving: "100g" },
  { id: 453, name: "Vegetable poha", calories: 181, serving: "100g" },
  { id: 454, name: "Moong dal stuffed cheela/chilla (Moong dal ka cheela/chilla)", calories: 155, serving: "100g" },
  { id: 455, name: "Paneer stuffed cheela/chilla", calories: 205, serving: "100g" },
  { id: 456, name: "Indian style egg bhujia (Anda bhujia (Indian style))", calories: 103, serving: "100g" },
  { id: 457, name: "French omelette/omlet", calories: 211, serving: "100g" },
  { id: 458, name: "Puffy omelette/omlet", calories: 188, serving: "100g" },
  { id: 459, name: "Orange omelette/omlet", calories: 195, serving: "100g" },
  { id: 460, name: "Cheese and mushroom omelette/omlet", calories: 206, serving: "100g" },
  { id: 461, name: "Spanish omelette/omlet", calories: 157, serving: "100g" },
  { id: 462, name: "Mixed stock", calories: 25, serving: "100g" },
  { id: 463, name: "Meat stock", calories: 24, serving: "100g" },
  { id: 464, name: "White stock", calories: 7, serving: "100g" },
  { id: 465, name: "Meat consomme (with mutton)", calories: 30, serving: "100g" },
  { id: 466, name: "Consomme au julienne", calories: 28, serving: "100g" },
  { id: 467, name: "Consomme au vermicelli", calories: 30, serving: "100g" },
  { id: 468, name: "Green pea soup (Matar ka soup)", calories: 40, serving: "100g" },
  { id: 469, name: "Spinach soup (Palak ka soup)", calories: 33, serving: "100g" },
  { id: 470, name: "Mixed vegetable soup", calories: 36, serving: "100g" },
  { id: 471, name: "Cheese soup", calories: 41, serving: "100g" },
  { id: 472, name: "Mulligatawny soup", calories: 54, serving: "100g" },
  { id: 473, name: "Cream of carrot soup", calories: 60, serving: "100g" },
  { id: 474, name: "Cream of broccoli soup", calories: 56, serving: "100g" },
  { id: 475, name: "Cream of potato soup", calories: 60, serving: "100g" },
  { id: 476, name: "Almond soup (Badam ka soup)", calories: 79, serving: "100g" },
  { id: 477, name: "Cold cucumber cream soup ", calories: 12, serving: "100g" },
  { id: 478, name: "Cold tomato soup", calories: 17, serving: "100g" },
  { id: 479, name: "Chicken stew", calories: 128, serving: "100g" },
  { id: 480, name: "Veg paneer stew", calories: 146, serving: "100g" },
  { id: 481, name: "Onion-green chilli parantha/paratha (Pyaaz aur hari mirch ka parantha/paratha)", calories: 191, serving: "100g" },
  { id: 482, name: "Methi parantha/paratha", calories: 148, serving: "100g" },
  { id: 483, name: "Bathua poori", calories: 599, serving: "100g" },
  { id: 484, name: "Gram flour poori (Besan poori)", calories: 698, serving: "100g" },
  { id: 485, name: "Beetroot poori (Chukandar ki poori)", calories: 528, serving: "100g" },
  { id: 486, name: "Peas poori (Matar ki poori)", calories: 593, serving: "100g" },
  { id: 487, name: "Peas kachori (Matar kachori)", calories: 585, serving: "100g" },
  { id: 488, name: "Pizza", calories: 147, serving: "100g" },
  { id: 489, name: "Bacon and mushroom pancake", calories: 158, serving: "100g" },
  { id: 490, name: "Cheese and tomato pancake", calories: 147, serving: "100g" },
  { id: 491, name: "Minced meat pancake (with chicken)", calories: 116, serving: "100g" },
  { id: 492, name: "Eggplant/Brinjal rice (Vangi bhat)", calories: 185, serving: "100g" },
  { id: 493, name: "Kashmiri 'tahar'", calories: 98, serving: "100g" },
  { id: 494, name: "Cumin pulao (Jeera/Zeera pulao)", calories: 135, serving: "100g" },
  { id: 495, name: "Peas pulao (Matar pulao)", calories: 109, serving: "100g" },
  { id: 496, name: "Split bengal gram dal and vegetable pulao (Channa dal and vegetable pulao)", calories: 117, serving: "100g" },
  { id: 497, name: "Black channa pulao/ Bengal gram pulao (Kale chane ka pulao)", calories: 126, serving: "100g" },
  { id: 498, name: "Mutton pulao", calories: 131, serving: "100g" },
  { id: 499, name: "Chicken pulao", calories: 108, serving: "100g" },
];

// Function to get all foods (base + custom)
export const getAllFoods = (): FoodItem[] => {
  const customFoods = getCustomFoods();
  const customFoodItems: FoodItem[] = customFoods.map(food => ({
    id: food.id,
    name: food.name,
    calories: food.calories,
    serving: food.serving,
    isCustom: true
  }));

  return [...baseFoodDatabase, ...customFoodItems];
};

// Export the combined database for backward compatibility
export const foodDatabase = getAllFoods();
