// Script to update food database with protein information from CSV
// This will be used to regenerate the food database with protein data

import { FoodItem } from './customFoodManager';

// Function to parse CSV data and create food items with protein information
export const createFoodDatabaseFromCSV = (csvData: string): FoodItem[] => {
  const lines = csvData.split('\n');
  const foods: FoodItem[] = [];
  
  // Skip header line
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    // Parse CSV line (handling commas within quotes)
    const columns = parseCSVLine(line);
    if (columns.length < 4) continue;
    
    const dishName = columns[0];
    const calories = parseFloat(columns[1]);
    const protein = parseFloat(columns[3]); // Protein is in column 4 (index 3)
    
    if (dishName && !isNaN(calories) && !isNaN(protein)) {
      foods.push({
        id: i, // Use line number as ID
        name: dishName,
        calories: Math.round(calories),
        protein: Math.round(protein * 10) / 10, // Round to 1 decimal place
        serving: "100g"
      });
    }
  }
  
  return foods;
};

// Helper function to parse CSV line handling commas within quotes
const parseCSVLine = (line: string): string[] => {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current.trim());
  return result;
};

// Generate TypeScript code for the food database
export const generateFoodDatabaseCode = (foods: FoodItem[]): string => {
  const foodsCode = foods.map(food => 
    `  { id: ${food.id}, name: "${food.name}", calories: ${food.calories}, protein: ${food.protein}, serving: "${food.serving}" }`
  ).join(',\n');
  
  return `
import { getCustomFoods, type FoodItem } from './customFoodManager';

// Indian Food Nutrition Database with Protein Information
// Data source: Indian_Food_Nutrition_Processed.csv
export const baseFoodDatabase: FoodItem[] = [
${foodsCode}
];

// Get all foods (base + custom)
export const getAllFoods = (): FoodItem[] => {
  const customFoods = getCustomFoods();
  return [...baseFoodDatabase, ...customFoods];
};

// Search foods by name
export const searchFoods = (query: string): FoodItem[] => {
  const allFoods = getAllFoods();
  const searchTerm = query.toLowerCase().trim();
  
  if (!searchTerm) return allFoods;
  
  return allFoods.filter(food =>
    food.name.toLowerCase().includes(searchTerm)
  );
};

// Get food by ID
export const getFoodById = (id: number): FoodItem | undefined => {
  const allFoods = getAllFoods();
  return allFoods.find(food => food.id === id);
};
`;
};
