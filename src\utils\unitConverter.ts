// Unit Conversion Utility
// Handles conversion between different units and calorie calculations

export interface UnitOption {
  value: string;
  label: string;
  type: 'weight' | 'volume' | 'piece';
  baseMultiplier: number; // Multiplier to convert to base unit (100g for weight, 100ml for volume)
}

export interface ConversionResult {
  amount: number;
  unit: string;
  calories: number;
  protein: number;
  baseEquivalent: number; // Amount in base units (100g or 100ml)
}

// Available units for conversion
export const UNIT_OPTIONS: UnitOption[] = [
  // Weight units (base: 100g)
  { value: 'g', label: 'grams (g)', type: 'weight', baseMultiplier: 0.01 },
  { value: '100g', label: '100 grams', type: 'weight', baseMultiplier: 1 },
  { value: 'kg', label: 'kilograms (kg)', type: 'weight', baseMultiplier: 10 },
  { value: 'oz', label: 'ounces (oz)', type: 'weight', baseMultiplier: 0.283495 },
  { value: 'lb', label: 'pounds (lb)', type: 'weight', baseMultiplier: 4.53592 },
  
  // Volume units (base: 100ml)
  { value: 'ml', label: 'milliliters (ml)', type: 'volume', baseMultiplier: 0.01 },
  { value: '100ml', label: '100 milliliters', type: 'volume', baseMultiplier: 1 },
  { value: 'l', label: 'liters (l)', type: 'volume', baseMultiplier: 10 },
  { value: 'cup', label: 'cups', type: 'volume', baseMultiplier: 2.36588 }, // 1 cup = 236.588ml
  { value: 'tbsp', label: 'tablespoons', type: 'volume', baseMultiplier: 0.147868 }, // 1 tbsp = 14.7868ml
  { value: 'tsp', label: 'teaspoons', type: 'volume', baseMultiplier: 0.0492892 }, // 1 tsp = 4.92892ml
  { value: 'fl-oz', label: 'fluid ounces', type: 'volume', baseMultiplier: 0.295735 }, // 1 fl oz = 29.5735ml
  
  // Piece units (base: 1 piece)
  { value: 'piece', label: 'pieces', type: 'piece', baseMultiplier: 1 },
  { value: 'slice', label: 'slices', type: 'piece', baseMultiplier: 1 },
  { value: 'serving', label: 'servings', type: 'piece', baseMultiplier: 1 },
];

// Get units by type
export const getUnitsByType = (type: 'weight' | 'volume' | 'piece'): UnitOption[] => {
  return UNIT_OPTIONS.filter(unit => unit.type === type);
};

// Get all weight and volume units (for foods with nutritional data per 100g/100ml)
export const getMeasurableUnits = (): UnitOption[] => {
  return UNIT_OPTIONS.filter(unit => unit.type === 'weight' || unit.type === 'volume');
};

// Convert amount from one unit to base units (100g or 100ml)
export const convertToBaseUnits = (amount: number, unit: string): number => {
  const unitOption = UNIT_OPTIONS.find(u => u.value === unit);
  if (!unitOption) {
    throw new Error(`Unknown unit: ${unit}`);
  }
  
  return amount * unitOption.baseMultiplier;
};

// Calculate calories and protein based on amount, unit, and food's nutritional density
export const calculateCalories = (
  amount: number,
  unit: string,
  caloriesPer100g: number,
  proteinPer100g: number = 0
): ConversionResult => {
  const unitOption = UNIT_OPTIONS.find(u => u.value === unit);
  if (!unitOption) {
    throw new Error(`Unknown unit: ${unit}`);
  }

  // For piece-based units, use direct multiplication
  if (unitOption.type === 'piece') {
    return {
      amount,
      unit: unitOption.label,
      calories: Math.round(amount * caloriesPer100g),
      protein: Math.round((amount * proteinPer100g) * 10) / 10, // Round to 1 decimal
      baseEquivalent: amount
    };
  }

  // For weight/volume units, convert to base units first
  const baseEquivalent = convertToBaseUnits(amount, unit);
  const calories = Math.round(baseEquivalent * caloriesPer100g);
  const protein = Math.round((baseEquivalent * proteinPer100g) * 10) / 10; // Round to 1 decimal

  return {
    amount,
    unit: unitOption.label,
    calories,
    protein,
    baseEquivalent
  };
};

// Validate amount input
export const validateAmount = (amount: number): { isValid: boolean; error?: string } => {
  if (!amount || isNaN(amount)) {
    return { isValid: false, error: 'Amount must be a valid number' };
  }
  
  if (amount <= 0) {
    return { isValid: false, error: 'Amount must be greater than 0' };
  }
  
  if (amount > 10000) {
    return { isValid: false, error: 'Amount seems too large (max: 10,000)' };
  }
  
  return { isValid: true };
};

// Get appropriate units for a food item based on its serving description and food name
export const getSuggestedUnits = (servingDescription: string, foodName?: string): UnitOption[] => {
  const serving = servingDescription.toLowerCase();
  const name = foodName?.toLowerCase() || '';

  // Check food name for beverage indicators first (overrides serving description)
  if (name.includes('tea') || name.includes('coffee') || name.includes('milk') ||
      name.includes('juice') || name.includes('water') || name.includes('drink') ||
      name.includes('lassi') || name.includes('shake') || name.includes('cocoa') ||
      name.includes('lemonade') || name.includes('punch') || name.includes('cooler')) {
    return getUnitsByType('volume');
  }

  // Check for explicit volume indicators in serving
  if (serving.includes('ml') || serving.includes('l') || serving.includes('liter') ||
      serving.includes('cup') || serving.includes('tbsp') || serving.includes('tsp')) {
    return getUnitsByType('volume');
  }

  // Check for piece indicators
  if (serving.includes('piece') || serving.includes('slice') || serving.includes('serving') ||
      serving.includes('item') || serving.includes('unit')) {
    return getUnitsByType('piece');
  }

  // Check for weight indicators (after checking beverages)
  if (serving.includes('g') || serving.includes('gram') || serving.includes('kg') || serving.includes('kilogram')) {
    return getUnitsByType('weight');
  }

  // Default: return all units with weight first for solid foods
  return [...getUnitsByType('weight'), ...getUnitsByType('volume'), ...getUnitsByType('piece')];
};

// Format conversion result for display
export const formatConversionResult = (result: ConversionResult): string => {
  return `${result.amount} ${result.unit} = ${result.calories} calories, ${result.protein}g protein`;
};

// Get unit label by value
export const getUnitLabel = (unitValue: string): string => {
  const unit = UNIT_OPTIONS.find(u => u.value === unitValue);
  return unit ? unit.label : unitValue;
};

// Check if a unit is weight-based
export const isWeightUnit = (unitValue: string): boolean => {
  const unit = UNIT_OPTIONS.find(u => u.value === unitValue);
  return unit ? unit.type === 'weight' : false;
};

// Check if a unit is volume-based
export const isVolumeUnit = (unitValue: string): boolean => {
  const unit = UNIT_OPTIONS.find(u => u.value === unitValue);
  return unit ? unit.type === 'volume' : false;
};

// Check if a unit is piece-based
export const isPieceUnit = (unitValue: string): boolean => {
  const unit = UNIT_OPTIONS.find(u => u.value === unitValue);
  return unit ? unit.type === 'piece' : false;
};

// Convert between different units of the same type
export const convertBetweenUnits = (
  amount: number, 
  fromUnit: string, 
  toUnit: string
): number => {
  const fromUnitOption = UNIT_OPTIONS.find(u => u.value === fromUnit);
  const toUnitOption = UNIT_OPTIONS.find(u => u.value === toUnit);
  
  if (!fromUnitOption || !toUnitOption) {
    throw new Error('Invalid unit conversion');
  }
  
  if (fromUnitOption.type !== toUnitOption.type) {
    throw new Error('Cannot convert between different unit types');
  }
  
  // Convert to base units, then to target units
  const baseAmount = amount * fromUnitOption.baseMultiplier;
  return baseAmount / toUnitOption.baseMultiplier;
};

// Get common conversions for a unit type
export const getCommonConversions = (unitType: 'weight' | 'volume' | 'piece'): string[] => {
  switch (unitType) {
    case 'weight':
      return ['1 kg = 1000 g', '1 lb = 453.6 g', '1 oz = 28.35 g'];
    case 'volume':
      return ['1 l = 1000 ml', '1 cup = 236.6 ml', '1 tbsp = 14.8 ml', '1 tsp = 4.9 ml'];
    case 'piece':
      return ['Use for countable items like slices, pieces, servings'];
    default:
      return [];
  }
};
