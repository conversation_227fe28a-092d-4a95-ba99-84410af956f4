import { useState, useEffect } from 'react';
import { Target, Save, RotateCcw, Info, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  getUserSettings, 
  updateDailyCalorieGoal, 
  validateCalorieGoal, 
  getRecommendedGoals,
  resetSettingsToDefault,
  type UserSettings 
} from '@/utils/settingsManager';

interface CalorieGoalSettingsProps {
  onGoalUpdated: (newGoal: number) => void;
  currentGoal: number;
}

const CalorieGoalSettings = ({ onGoalUpdated, currentGoal }: CalorieGoalSettingsProps) => {
  const [goalInput, setGoalInput] = useState(currentGoal.toString());
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [showRecommendations, setShowRecommendations] = useState(false);

  const recommendedGoals = getRecommendedGoals();

  useEffect(() => {
    setGoalInput(currentGoal.toString());
  }, [currentGoal]);

  const handleSave = async () => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    const goalValue = parseInt(goalInput);
    const validation = validateCalorieGoal(goalValue);

    if (!validation.isValid) {
      setError(validation.error || 'Invalid calorie goal');
      setIsLoading(false);
      return;
    }

    try {
      updateDailyCalorieGoal(goalValue);
      onGoalUpdated(goalValue);
      setSuccess('Daily calorie goal updated successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('Failed to update calorie goal. Please try again.');
    }

    setIsLoading(false);
  };

  const handleReset = () => {
    const defaultSettings = resetSettingsToDefault();
    setGoalInput(defaultSettings.dailyCalorieGoal.toString());
    onGoalUpdated(defaultSettings.dailyCalorieGoal);
    setSuccess('Settings reset to default!');
    setError('');
    
    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleRecommendedGoalSelect = (goal: number) => {
    setGoalInput(goal.toString());
    setError('');
    setShowRecommendations(false);
  };

  const handleInputChange = (value: string) => {
    setGoalInput(value);
    setError('');
    setSuccess('');
  };

  const hasChanges = parseInt(goalInput) !== currentGoal;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center text-xl">
          <Target className="mr-2 h-5 w-5 text-green-600" />
          Daily Calorie Goal Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {success && (
          <Alert className="border-green-200 bg-green-50">
            <Check className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              {success}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Current Goal Display */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-blue-900">Current Daily Goal</h3>
              <p className="text-sm text-blue-700">Your target calories per day</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">{currentGoal}</div>
              <div className="text-sm text-blue-500">calories</div>
            </div>
          </div>
        </div>

        {/* Goal Input */}
        <div className="space-y-2">
          <Label htmlFor="calorie-goal">New Daily Calorie Goal</Label>
          <div className="flex space-x-2">
            <Input
              id="calorie-goal"
              type="number"
              placeholder="e.g., 2000"
              value={goalInput}
              onChange={(e) => handleInputChange(e.target.value)}
              className="flex-1"
              min="800"
              max="10000"
              step="50"
            />
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              <Save className="mr-2 h-4 w-4" />
              {isLoading ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>

        {/* Recommended Goals */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-900">Recommended Goals</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowRecommendations(!showRecommendations)}
            >
              <Info className="mr-2 h-4 w-4" />
              {showRecommendations ? 'Hide' : 'Show'} Recommendations
            </Button>
          </div>

          {showRecommendations && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {recommendedGoals.map((goal, index) => (
                <div
                  key={index}
                  className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleRecommendedGoalSelect(goal.value)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-sm">{goal.label}</span>
                    <Badge variant="secondary">{goal.value} cal</Badge>
                  </div>
                  <p className="text-xs text-gray-600">{goal.description}</p>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleReset}
            className="text-gray-600"
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset to Default
          </Button>
          
          {hasChanges && (
            <div className="text-sm text-amber-600 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              You have unsaved changes
            </div>
          )}
        </div>

        {/* Information */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Tips for Setting Your Goal</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Consider your age, gender, weight, height, and activity level</li>
            <li>• For weight loss: Create a deficit of 500-750 calories per day</li>
            <li>• For weight gain: Add 300-500 calories above maintenance</li>
            <li>• Consult a healthcare provider for personalized recommendations</li>
            <li>• Minimum recommended: 800 calories (very low calorie diets need supervision)</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default CalorieGoalSettings;
