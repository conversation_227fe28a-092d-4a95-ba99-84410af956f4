
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Activity, Target, TrendingUp, Edit } from 'lucide-react';

interface CalorieStatsProps {
  totalCalories: number;
  dailyGoal: number;
  remainingCalories: number;
  progressPercentage: number;
  onEditGoal?: () => void;
}

const CalorieStats = ({ totalCalories, dailyGoal, remainingCalories, progressPercentage, onEditGoal }: CalorieStatsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <Card className="text-center shadow-lg border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-xl transition-shadow duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 flex items-center justify-center">
            <Activity className="mr-2 h-4 w-4 text-blue-500 dark:text-blue-400" />
            Consumed Today
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">{totalCalories}</div>
          <p className="text-sm text-gray-500 dark:text-gray-400">calories</p>
        </CardContent>
      </Card>

      <Card
        className={`text-center shadow-lg border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-xl transition-all duration-200 ${
          onEditGoal ? 'cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 hover:border-green-200 dark:hover:border-green-700' : ''
        }`}
        onClick={onEditGoal}
      >
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 flex items-center justify-center">
            <Target className="mr-2 h-4 w-4 text-green-500 dark:text-green-400" />
            Daily Goal
            {onEditGoal && <Edit className="ml-2 h-3 w-3 text-gray-400 dark:text-gray-500" />}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-green-600 dark:text-green-400">{dailyGoal}</div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            calories {onEditGoal && <span className="text-xs text-green-600 dark:text-green-400">(click to edit)</span>}
          </p>
        </CardContent>
      </Card>

      <Card className="text-center shadow-lg border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-xl transition-shadow duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 flex items-center justify-center">
            <TrendingUp className="mr-2 h-4 w-4 text-purple-500 dark:text-purple-400" />
            Remaining
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`text-3xl font-bold ${remainingCalories >= 0 ? 'text-purple-600 dark:text-purple-400' : 'text-red-500 dark:text-red-400'}`}>
            {Math.abs(remainingCalories)}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {remainingCalories >= 0 ? 'calories left' : 'calories over'}
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default CalorieStats;
