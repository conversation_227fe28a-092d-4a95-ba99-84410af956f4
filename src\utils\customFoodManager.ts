// Custom Food Management Utility
// Handles adding, storing, and retrieving custom foods

export interface CustomFood {
  id: number;
  name: string;
  calories: number;
  serving: string;
  isCustom: boolean;
  dateAdded: string;
}

export interface FoodItem {
  id: number;
  name: string;
  calories: number;
  serving: string;
  isCustom?: boolean;
}

const CUSTOM_FOODS_KEY = 'custom-foods';

// Get all custom foods from localStorage
export const getCustomFoods = (): CustomFood[] => {
  try {
    const stored = localStorage.getItem(CUSTOM_FOODS_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading custom foods:', error);
    return [];
  }
};

// Save custom foods to localStorage
export const saveCustomFoods = (foods: CustomFood[]): void => {
  try {
    localStorage.setItem(CUSTOM_FOODS_KEY, JSON.stringify(foods));
  } catch (error) {
    console.error('Error saving custom foods:', error);
  }
};

// Add a new custom food
export const addCustomFood = (food: Omit<CustomFood, 'id' | 'isCustom' | 'dateAdded'>): CustomFood => {
  const customFoods = getCustomFoods();
  
  // Generate new ID (start from 10000 to avoid conflicts with existing database)
  const newId = customFoods.length > 0 
    ? Math.max(...customFoods.map(f => f.id)) + 1 
    : 10000;
  
  const newCustomFood: CustomFood = {
    ...food,
    id: newId,
    isCustom: true,
    dateAdded: new Date().toISOString()
  };
  
  const updatedFoods = [...customFoods, newCustomFood];
  saveCustomFoods(updatedFoods);
  
  return newCustomFood;
};

// Remove a custom food
export const removeCustomFood = (id: number): void => {
  const customFoods = getCustomFoods();
  const updatedFoods = customFoods.filter(food => food.id !== id);
  saveCustomFoods(updatedFoods);
};

// Get the next available ID for new custom foods
export const getNextCustomFoodId = (): number => {
  const customFoods = getCustomFoods();
  return customFoods.length > 0 
    ? Math.max(...customFoods.map(f => f.id)) + 1 
    : 10000;
};

// Check if a food name already exists (case-insensitive)
export const foodNameExists = (name: string, allFoods: FoodItem[]): boolean => {
  const normalizedName = name.toLowerCase().trim();
  return allFoods.some(food => 
    food.name.toLowerCase().trim() === normalizedName
  );
};

// Validate custom food data
export const validateCustomFood = (food: {
  name: string;
  calories: number;
  serving: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Validate name
  if (!food.name || food.name.trim().length === 0) {
    errors.push('Food name is required');
  } else if (food.name.trim().length < 2) {
    errors.push('Food name must be at least 2 characters long');
  } else if (food.name.trim().length > 100) {
    errors.push('Food name must be less than 100 characters');
  }
  
  // Validate calories
  if (!food.calories || food.calories <= 0) {
    errors.push('Calories must be a positive number');
  } else if (food.calories > 10000) {
    errors.push('Calories must be less than 10,000');
  }
  
  // Validate serving
  if (!food.serving || food.serving.trim().length === 0) {
    errors.push('Serving size is required');
  } else if (food.serving.trim().length > 50) {
    errors.push('Serving size must be less than 50 characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Export custom foods to JSON for backup
export const exportCustomFoods = (): string => {
  const customFoods = getCustomFoods();
  return JSON.stringify(customFoods, null, 2);
};

// Import custom foods from JSON
export const importCustomFoods = (jsonData: string): { success: boolean; message: string; count?: number } => {
  try {
    const importedFoods = JSON.parse(jsonData);
    
    if (!Array.isArray(importedFoods)) {
      return { success: false, message: 'Invalid format: Expected an array of foods' };
    }
    
    // Validate each food item
    const validFoods: CustomFood[] = [];
    for (const food of importedFoods) {
      if (food.name && food.calories && food.serving) {
        validFoods.push({
          ...food,
          id: getNextCustomFoodId() + validFoods.length,
          isCustom: true,
          dateAdded: food.dateAdded || new Date().toISOString()
        });
      }
    }
    
    if (validFoods.length === 0) {
      return { success: false, message: 'No valid food items found in the import data' };
    }
    
    // Merge with existing custom foods
    const existingFoods = getCustomFoods();
    const mergedFoods = [...existingFoods, ...validFoods];
    saveCustomFoods(mergedFoods);
    
    return { 
      success: true, 
      message: `Successfully imported ${validFoods.length} custom foods`,
      count: validFoods.length
    };
  } catch (error) {
    return { success: false, message: 'Invalid JSON format' };
  }
};
